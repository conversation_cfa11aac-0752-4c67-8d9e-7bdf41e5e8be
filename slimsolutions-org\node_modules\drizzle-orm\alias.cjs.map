{"version": 3, "sources": ["../src/alias.ts"], "sourcesContent": ["import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport type { Relation } from './relations.ts';\nimport type { View } from './sql/sql.ts';\nimport { SQL, sql } from './sql/sql.ts';\nimport { Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class ColumnAliasProxyHandler<TColumn extends Column> implements ProxyHandler<TColumn> {\n\tstatic readonly [entityKind]: string = 'ColumnAliasProxyHandler';\n\n\tconstructor(private table: Table | View) {}\n\n\tget(columnObj: TColumn, prop: string | symbol): any {\n\t\tif (prop === 'table') {\n\t\t\treturn this.table;\n\t\t}\n\n\t\treturn columnObj[prop as keyof TColumn];\n\t}\n}\n\nexport class TableAliasProxyHandler<T extends Table | View> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'TableAliasProxyHandler';\n\n\tconstructor(private alias: string, private replaceOriginalName: boolean) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === Table.Symbol.IsAlias) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (prop === Table.Symbol.Name) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (this.replaceOriginalName && prop === Table.Symbol.OriginalName) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...target[ViewBaseConfig as keyof typeof target],\n\t\t\t\tname: this.alias,\n\t\t\t\tisAlias: true,\n\t\t\t};\n\t\t}\n\n\t\tif (prop === Table.Symbol.Columns) {\n\t\t\tconst columns = (target as Table)[Table.Symbol.Columns];\n\t\t\tif (!columns) {\n\t\t\t\treturn columns;\n\t\t\t}\n\n\t\t\tconst proxiedColumns: { [key: string]: any } = {};\n\n\t\t\tObject.keys(columns).map((key) => {\n\t\t\t\tproxiedColumns[key] = new Proxy(\n\t\t\t\t\tcolumns[key]!,\n\t\t\t\t\tnew ColumnAliasProxyHandler(new Proxy(target, this)),\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn proxiedColumns;\n\t\t}\n\n\t\tconst value = target[prop as keyof typeof target];\n\t\tif (is(value, Column)) {\n\t\t\treturn new Proxy(value as AnyColumn, new ColumnAliasProxyHandler(new Proxy(target, this)));\n\t\t}\n\n\t\treturn value;\n\t}\n}\n\nexport class RelationTableAliasProxyHandler<T extends Relation> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'RelationTableAliasProxyHandler';\n\n\tconstructor(private alias: string) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === 'sourceTable') {\n\t\t\treturn aliasedTable(target.sourceTable, this.alias);\n\t\t}\n\n\t\treturn target[prop as keyof typeof target];\n\t}\n}\n\nexport function aliasedTable<T extends Table | View>(\n\ttable: T,\n\ttableAlias: string,\n): T {\n\treturn new Proxy(table, new TableAliasProxyHandler(tableAlias, false)) as any;\n}\n\nexport function aliasedRelation<T extends Relation>(relation: T, tableAlias: string): T {\n\treturn new Proxy(relation, new RelationTableAliasProxyHandler(tableAlias));\n}\n\nexport function aliasedTableColumn<T extends AnyColumn>(column: T, tableAlias: string): T {\n\treturn new Proxy(\n\t\tcolumn,\n\t\tnew ColumnAliasProxyHandler(new Proxy(column.table, new TableAliasProxyHandler(tableAlias, false))),\n\t);\n}\n\nexport function mapColumnsInAliasedSQLToAlias(query: SQL.Aliased, alias: string): SQL.Aliased {\n\treturn new SQL.Aliased(mapColumnsInSQLToAlias(query.sql, alias), query.fieldAlias);\n}\n\nexport function mapColumnsInSQLToAlias(query: SQL, alias: string): SQL {\n\treturn sql.join(query.queryChunks.map((c) => {\n\t\tif (is(c, Column)) {\n\t\t\treturn aliasedTableColumn(c, alias);\n\t\t}\n\t\tif (is(c, SQL)) {\n\t\t\treturn mapColumnsInSQLToAlias(c, alias);\n\t\t}\n\t\tif (is(c, SQL.Aliased)) {\n\t\t\treturn mapColumnsInAliasedSQLToAlias(c, alias);\n\t\t}\n\t\treturn c;\n\t}));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAuB;AACvB,oBAA+B;AAG/B,iBAAyB;AACzB,mBAAsB;AACtB,yBAA+B;AAExB,MAAM,wBAAiF;AAAA,EAG7F,YAAoB,OAAqB;AAArB;AAAA,EAAsB;AAAA,EAF1C,QAAiB,wBAAU,IAAY;AAAA,EAIvC,IAAI,WAAoB,MAA4B;AACnD,QAAI,SAAS,SAAS;AACrB,aAAO,KAAK;AAAA,IACb;AAEA,WAAO,UAAU,IAAqB;AAAA,EACvC;AACD;AAEO,MAAM,uBAA0E;AAAA,EAGtF,YAAoB,OAAuB,qBAA8B;AAArD;AAAuB;AAAA,EAA+B;AAAA,EAF1E,QAAiB,wBAAU,IAAY;AAAA,EAIvC,IAAI,QAAW,MAA4B;AAC1C,QAAI,SAAS,mBAAM,OAAO,SAAS;AAClC,aAAO;AAAA,IACR;AAEA,QAAI,SAAS,mBAAM,OAAO,MAAM;AAC/B,aAAO,KAAK;AAAA,IACb;AAEA,QAAI,KAAK,uBAAuB,SAAS,mBAAM,OAAO,cAAc;AACnE,aAAO,KAAK;AAAA,IACb;AAEA,QAAI,SAAS,mCAAgB;AAC5B,aAAO;AAAA,QACN,GAAG,OAAO,iCAAqC;AAAA,QAC/C,MAAM,KAAK;AAAA,QACX,SAAS;AAAA,MACV;AAAA,IACD;AAEA,QAAI,SAAS,mBAAM,OAAO,SAAS;AAClC,YAAM,UAAW,OAAiB,mBAAM,OAAO,OAAO;AACtD,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AAEA,YAAM,iBAAyC,CAAC;AAEhD,aAAO,KAAK,OAAO,EAAE,IAAI,CAAC,QAAQ;AACjC,uBAAe,GAAG,IAAI,IAAI;AAAA,UACzB,QAAQ,GAAG;AAAA,UACX,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC;AAAA,QACpD;AAAA,MACD,CAAC;AAED,aAAO;AAAA,IACR;AAEA,UAAM,QAAQ,OAAO,IAA2B;AAChD,YAAI,kBAAG,OAAO,oBAAM,GAAG;AACtB,aAAO,IAAI,MAAM,OAAoB,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC;AAAA,IAC1F;AAEA,WAAO;AAAA,EACR;AACD;AAEO,MAAM,+BAA8E;AAAA,EAG1F,YAAoB,OAAe;AAAf;AAAA,EAAgB;AAAA,EAFpC,QAAiB,wBAAU,IAAY;AAAA,EAIvC,IAAI,QAAW,MAA4B;AAC1C,QAAI,SAAS,eAAe;AAC3B,aAAO,aAAa,OAAO,aAAa,KAAK,KAAK;AAAA,IACnD;AAEA,WAAO,OAAO,IAA2B;AAAA,EAC1C;AACD;AAEO,SAAS,aACf,OACA,YACI;AACJ,SAAO,IAAI,MAAM,OAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC;AACtE;AAEO,SAAS,gBAAoC,UAAa,YAAuB;AACvF,SAAO,IAAI,MAAM,UAAU,IAAI,+BAA+B,UAAU,CAAC;AAC1E;AAEO,SAAS,mBAAwC,QAAW,YAAuB;AACzF,SAAO,IAAI;AAAA,IACV;AAAA,IACA,IAAI,wBAAwB,IAAI,MAAM,OAAO,OAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC,CAAC;AAAA,EACnG;AACD;AAEO,SAAS,8BAA8B,OAAoB,OAA4B;AAC7F,SAAO,IAAI,eAAI,QAAQ,uBAAuB,MAAM,KAAK,KAAK,GAAG,MAAM,UAAU;AAClF;AAEO,SAAS,uBAAuB,OAAY,OAAoB;AACtE,SAAO,eAAI,KAAK,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5C,YAAI,kBAAG,GAAG,oBAAM,GAAG;AAClB,aAAO,mBAAmB,GAAG,KAAK;AAAA,IACnC;AACA,YAAI,kBAAG,GAAG,cAAG,GAAG;AACf,aAAO,uBAAuB,GAAG,KAAK;AAAA,IACvC;AACA,YAAI,kBAAG,GAAG,eAAI,OAAO,GAAG;AACvB,aAAO,8BAA8B,GAAG,KAAK;AAAA,IAC9C;AACA,WAAO;AAAA,EACR,CAAC,CAAC;AACH;", "names": []}
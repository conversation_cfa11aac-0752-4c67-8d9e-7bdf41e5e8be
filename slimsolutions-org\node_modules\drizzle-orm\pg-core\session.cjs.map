{"version": 3, "sources": ["../../src/pg-core/session.ts"], "sourcesContent": ["import { type Cache, hashQuery, NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport { DrizzleQueryError } from '~/errors/index.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { type Query, type SQL, sql } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { NeonAuthToken } from '~/utils.ts';\nimport { PgDatabase } from './db.ts';\nimport type { PgDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class PgPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(\n\t\tprotected query: Query,\n\t\t// cache instance\n\t\tprivate cache: Cache | undefined,\n\t\t// per query related metadata\n\t\tprivate queryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\t// config that was passed through $withCache\n\t\tprivate cacheConfig?: WithCacheConfig,\n\t) {\n\t\t// it means that no $withCache options were passed and it should be just enabled\n\t\tif (cache && cache.strategy() === 'all' && cacheConfig === undefined) {\n\t\t\tthis.cacheConfig = { enable: true, autoInvalidate: true };\n\t\t}\n\t\tif (!this.cacheConfig?.enable) {\n\t\t\tthis.cacheConfig = undefined;\n\t\t}\n\t}\n\n\tprotected authToken?: NeonAuthToken;\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tstatic readonly [entityKind]: string = 'PgPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\t/** @internal */\n\tprotected async queryWithCache<T>(\n\t\tqueryString: string,\n\t\tparams: any[],\n\t\tquery: () => Promise<T>,\n\t): Promise<T> {\n\t\tif (this.cache === undefined || is(this.cache, NoopCache) || this.queryMetadata === undefined) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any mutations, if globally is false\n\t\tif (this.cacheConfig && !this.cacheConfig.enable) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// For mutate queries, we should query the database, wait for a response, and then perform invalidation\n\t\tif (\n\t\t\t(\n\t\t\t\tthis.queryMetadata.type === 'insert' || this.queryMetadata.type === 'update'\n\t\t\t\t|| this.queryMetadata.type === 'delete'\n\t\t\t) && this.queryMetadata.tables.length > 0\n\t\t) {\n\t\t\ttry {\n\t\t\t\tconst [res] = await Promise.all([\n\t\t\t\t\tquery(),\n\t\t\t\t\tthis.cache.onMutate({ tables: this.queryMetadata.tables }),\n\t\t\t\t]);\n\t\t\t\treturn res;\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any reads if globally disabled\n\t\tif (!this.cacheConfig) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\tif (this.queryMetadata.type === 'select') {\n\t\t\tconst fromCache = await this.cache.get(\n\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\tthis.queryMetadata.tables,\n\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\tthis.cacheConfig.autoInvalidate,\n\t\t\t);\n\t\t\tif (fromCache === undefined) {\n\t\t\t\tlet result;\n\t\t\t\ttry {\n\t\t\t\t\tresult = await query();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t\t}\n\t\t\t\t// put actual key\n\t\t\t\tawait this.cache.put(\n\t\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\t\tresult,\n\t\t\t\t\t// make sure we send tables that were used in a query only if user wants to invalidate it on each write\n\t\t\t\t\tthis.cacheConfig.autoInvalidate ? this.queryMetadata.tables : [],\n\t\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\t\tthis.cacheConfig.config,\n\t\t\t\t);\n\t\t\t\t// put flag if we should invalidate or not\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\treturn fromCache as unknown as T;\n\t\t}\n\t\ttry {\n\t\t\treturn await query();\n\t\t} catch (e) {\n\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t}\n\t}\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: NeonAuthToken): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: NeonAuthToken): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface PgTransactionConfig {\n\tisolationLevel?: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n\taccessMode?: 'read only' | 'read write';\n\tdeferrable?: boolean;\n}\n\nexport abstract class PgSession<\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'PgSession';\n\n\tconstructor(protected dialect: PgDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: NeonAuthToken): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: NeonAuthToken): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.setToken(token).execute(undefined, token);\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tasync count(sql: SQL): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: NeonAuthToken): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: NeonAuthToken): Promise<number> {\n\t\tconst res = await this.execute<[{ count: string }]>(sql, token);\n\n\t\treturn Number(\n\t\t\tres[0]['count'],\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T>;\n}\n\nexport abstract class PgTransaction<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends PgDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tsession: PgSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** @internal */\n\tgetTransactionConfigSQL(config: PgTransactionConfig): SQL {\n\t\tconst chunks: string[] = [];\n\t\tif (config.isolationLevel) {\n\t\t\tchunks.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\t\tif (config.accessMode) {\n\t\t\tchunks.push(config.accessMode);\n\t\t}\n\t\tif (typeof config.deferrable === 'boolean') {\n\t\t\tchunks.push(config.deferrable ? 'deferrable' : 'not deferrable');\n\t\t}\n\t\treturn sql.raw(chunks.join(' '));\n\t}\n\n\tsetTransaction(config: PgTransactionConfig): Promise<void> {\n\t\treturn this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PgQueryResultHKT {\n\treadonly $brand: 'PgQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type PgQueryResultKind<TKind extends PgQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAiD;AAEjD,oBAA+B;AAC/B,oBAAyC;AACzC,IAAAA,iBAAkC;AAGlC,iBAA0C;AAC1C,qBAAuB;AAEvB,gBAA2B;AAUpB,MAAe,gBAAwE;AAAA,EAC7F,YACW,OAEF,OAEA,eAKA,aACP;AAVS;AAEF;AAEA;AAKA;AAGR,QAAI,SAAS,MAAM,SAAS,MAAM,SAAS,gBAAgB,QAAW;AACrE,WAAK,cAAc,EAAE,QAAQ,MAAM,gBAAgB,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,aAAa,QAAQ;AAC9B,WAAK,cAAc;AAAA,IACpB;AAAA,EACD;AAAA,EAEU;AAAA,EAEV,WAAkB;AACjB,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,UAAU,UAAmB,cAAiC;AAC7D,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGA,MAAgB,eACf,aACA,QACA,OACa;AACb,QAAI,KAAK,UAAU,cAAa,kBAAG,KAAK,OAAO,sBAAS,KAAK,KAAK,kBAAkB,QAAW;AAC9F,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,QAAQ;AACjD,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,SAEE,KAAK,cAAc,SAAS,YAAY,KAAK,cAAc,SAAS,YACjE,KAAK,cAAc,SAAS,aAC3B,KAAK,cAAc,OAAO,SAAS,GACvC;AACD,UAAI;AACH,cAAM,CAAC,GAAG,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC/B,MAAM;AAAA,UACN,KAAK,MAAM,SAAS,EAAE,QAAQ,KAAK,cAAc,OAAO,CAAC;AAAA,QAC1D,CAAC;AACD,eAAO;AAAA,MACR,SAAS,GAAG;AACX,cAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,CAAC,KAAK,aAAa;AACtB,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAEA,QAAI,KAAK,cAAc,SAAS,UAAU;AACzC,YAAM,YAAY,MAAM,KAAK,MAAM;AAAA,QAClC,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,QAC3D,KAAK,cAAc;AAAA,QACnB,KAAK,YAAY,QAAQ;AAAA,QACzB,KAAK,YAAY;AAAA,MAClB;AACA,UAAI,cAAc,QAAW;AAC5B,YAAI;AACJ,YAAI;AACH,mBAAS,MAAM,MAAM;AAAA,QACtB,SAAS,GAAG;AACX,gBAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,QAC5D;AAEA,cAAM,KAAK,MAAM;AAAA,UAChB,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,UAC3D;AAAA;AAAA,UAEA,KAAK,YAAY,iBAAiB,KAAK,cAAc,SAAS,CAAC;AAAA,UAC/D,KAAK,YAAY,QAAQ;AAAA,UACzB,KAAK,YAAY;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR;AACA,QAAI;AACH,aAAO,MAAM,MAAM;AAAA,IACpB,SAAS,GAAG;AACX,YAAM,IAAI,iCAAkB,aAAa,QAAQ,CAAU;AAAA,IAC5D;AAAA,EACD;AAaD;AAQO,MAAe,UAIpB;AAAA,EAGD,YAAsB,SAAoB;AAApB;AAAA,EAAqB;AAAA,EAF3C,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAqBvC,QAAW,OAAY,OAAmC;AACzD,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,YAAM,WAAW,sBAAO,gBAAgB,wBAAwB,MAAM;AACrE,eAAO,KAAK;AAAA,UACX,KAAK,QAAQ,WAAW,KAAK;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD,CAAC;AAED,aAAO,SAAS,SAAS,KAAK,EAAE,QAAQ,QAAW,KAAK;AAAA,IACzD,CAAC;AAAA,EACF;AAAA,EAEA,IAAiB,OAA0B;AAC1C,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,IACD,EAAE,IAAI;AAAA,EACP;AAAA;AAAA,EAMA,MAAM,MAAMC,MAAU,OAAwC;AAC7D,UAAM,MAAM,MAAM,KAAK,QAA6BA,MAAK,KAAK;AAE9D,WAAO;AAAA,MACN,IAAI,CAAC,EAAE,OAAO;AAAA,IACf;AAAA,EACD;AAMD;AAEO,MAAe,sBAIZ,qBAA+C;AAAA,EAGxD,YACC,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,SAAS,SAAS,MAAM;AAPpB;AAKS;AAAA,EAGpB;AAAA,EAbA,QAA0B,wBAAU,IAAY;AAAA,EAehD,WAAkB;AACjB,UAAM,IAAI,uCAAyB;AAAA,EACpC;AAAA;AAAA,EAGA,wBAAwB,QAAkC;AACzD,UAAM,SAAmB,CAAC;AAC1B,QAAI,OAAO,gBAAgB;AAC1B,aAAO,KAAK,mBAAmB,OAAO,cAAc,EAAE;AAAA,IACvD;AACA,QAAI,OAAO,YAAY;AACtB,aAAO,KAAK,OAAO,UAAU;AAAA,IAC9B;AACA,QAAI,OAAO,OAAO,eAAe,WAAW;AAC3C,aAAO,KAAK,OAAO,aAAa,eAAe,gBAAgB;AAAA,IAChE;AACA,WAAO,eAAI,IAAI,OAAO,KAAK,GAAG,CAAC;AAAA,EAChC;AAAA,EAEA,eAAe,QAA4C;AAC1D,WAAO,KAAK,QAAQ,QAAQ,iCAAsB,KAAK,wBAAwB,MAAM,CAAC,EAAE;AAAA,EACzF;AAKD;", "names": ["import_errors", "sql"]}
{"version": 3, "sources": ["../../../src/pg-core/utils/array.ts"], "sourcesContent": ["function parsePgArrayValue(arrayString: string, startFrom: number, inQuotes: boolean): [string, number] {\n\tfor (let i = startFrom; i < arrayString.length; i++) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === '\\\\') {\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i + 1];\n\t\t}\n\n\t\tif (inQuotes) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === ',' || char === '}') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i];\n\t\t}\n\t}\n\n\treturn [arrayString.slice(startFrom).replace(/\\\\/g, ''), arrayString.length];\n}\n\nexport function parsePgNestedArray(arrayString: string, startFrom = 0): [any[], number] {\n\tconst result: any[] = [];\n\tlet i = startFrom;\n\tlet lastCharIsComma = false;\n\n\twhile (i < arrayString.length) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === ',') {\n\t\t\tif (lastCharIsComma || i === startFrom) {\n\t\t\t\tresult.push('');\n\t\t\t}\n\t\t\tlastCharIsComma = true;\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tlastCharIsComma = false;\n\n\t\tif (char === '\\\\') {\n\t\t\ti += 2;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\tconst [value, startFrom] = parsePgArrayValue(arrayString, i + 1, true);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '}') {\n\t\t\treturn [result, i + 1];\n\t\t}\n\n\t\tif (char === '{') {\n\t\t\tconst [value, startFrom] = parsePgNestedArray(arrayString, i + 1);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n\t\tresult.push(value);\n\t\ti = newStartFrom;\n\t}\n\n\treturn [result, i];\n}\n\nexport function parsePgArray(arrayString: string): any[] {\n\tconst [result] = parsePgNestedArray(arrayString, 1);\n\treturn result;\n}\n\nexport function makePgArray(array: any[]): string {\n\treturn `{${\n\t\tarray.map((item) => {\n\t\t\tif (Array.isArray(item)) {\n\t\t\t\treturn makePgArray(item);\n\t\t\t}\n\n\t\t\tif (typeof item === 'string') {\n\t\t\t\treturn `\"${item.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')}\"`;\n\t\t\t}\n\n\t\t\treturn `${item}`;\n\t\t}).join(',')\n\t}}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,kBAAkB,aAAqB,WAAmB,UAAqC;AACvG,WAAS,IAAI,WAAW,IAAI,YAAY,QAAQ,KAAK;AACpD,UAAM,OAAO,YAAY,CAAC;AAE1B,QAAI,SAAS,MAAM;AAClB;AACA;AAAA,IACD;AAEA,QAAI,SAAS,KAAK;AACjB,aAAO,CAAC,YAAY,MAAM,WAAW,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,IAAI,CAAC;AAAA,IAClE;AAEA,QAAI,UAAU;AACb;AAAA,IACD;AAEA,QAAI,SAAS,OAAO,SAAS,KAAK;AACjC,aAAO,CAAC,YAAY,MAAM,WAAW,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,CAAC;AAAA,IAC9D;AAAA,EACD;AAEA,SAAO,CAAC,YAAY,MAAM,SAAS,EAAE,QAAQ,OAAO,EAAE,GAAG,YAAY,MAAM;AAC5E;AAEO,SAAS,mBAAmB,aAAqB,YAAY,GAAoB;AACvF,QAAM,SAAgB,CAAC;AACvB,MAAI,IAAI;AACR,MAAI,kBAAkB;AAEtB,SAAO,IAAI,YAAY,QAAQ;AAC9B,UAAM,OAAO,YAAY,CAAC;AAE1B,QAAI,SAAS,KAAK;AACjB,UAAI,mBAAmB,MAAM,WAAW;AACvC,eAAO,KAAK,EAAE;AAAA,MACf;AACA,wBAAkB;AAClB;AACA;AAAA,IACD;AAEA,sBAAkB;AAElB,QAAI,SAAS,MAAM;AAClB,WAAK;AACL;AAAA,IACD;AAEA,QAAI,SAAS,KAAK;AACjB,YAAM,CAACA,QAAOC,UAAS,IAAI,kBAAkB,aAAa,IAAI,GAAG,IAAI;AACrE,aAAO,KAAKD,MAAK;AACjB,UAAIC;AACJ;AAAA,IACD;AAEA,QAAI,SAAS,KAAK;AACjB,aAAO,CAAC,QAAQ,IAAI,CAAC;AAAA,IACtB;AAEA,QAAI,SAAS,KAAK;AACjB,YAAM,CAACD,QAAOC,UAAS,IAAI,mBAAmB,aAAa,IAAI,CAAC;AAChE,aAAO,KAAKD,MAAK;AACjB,UAAIC;AACJ;AAAA,IACD;AAEA,UAAM,CAAC,OAAO,YAAY,IAAI,kBAAkB,aAAa,GAAG,KAAK;AACrE,WAAO,KAAK,KAAK;AACjB,QAAI;AAAA,EACL;AAEA,SAAO,CAAC,QAAQ,CAAC;AAClB;AAEO,SAAS,aAAa,aAA4B;AACxD,QAAM,CAAC,MAAM,IAAI,mBAAmB,aAAa,CAAC;AAClD,SAAO;AACR;AAEO,SAAS,YAAY,OAAsB;AACjD,SAAO,IACN,MAAM,IAAI,CAAC,SAAS;AACnB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACxB,aAAO,YAAY,IAAI;AAAA,IACxB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,IAAI,KAAK,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK,CAAC;AAAA,IAC5D;AAEA,WAAO,GAAG,IAAI;AAAA,EACf,CAAC,EAAE,KAAK,GAAG,CACZ;AACD;", "names": ["value", "startFrom"]}
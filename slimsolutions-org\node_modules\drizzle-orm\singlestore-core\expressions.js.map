{"version": 3, "sources": ["../../src/singlestore-core/expressions.ts"], "sourcesContent": ["import { bindIfParam } from '~/sql/expressions/index.ts';\nimport type { Placeholder, SQL, SQLChunk, SQLWrapper } from '~/sql/sql.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { SingleStoreColumn } from './columns/index.ts';\n\nexport * from '~/sql/expressions/index.ts';\n\nexport function concat(column: SingleStoreColumn | SQL.Aliased, value: string | Placeholder | SQLWrapper): SQL {\n\treturn sql`${column} || ${bindIfParam(value, column)}`;\n}\n\nexport function substring(\n\tcolumn: SingleStoreColumn | SQL.Aliased,\n\t{ from, for: _for }: { from?: number | Placeholder | SQLWrapper; for?: number | Placeholder | SQLWrapper },\n): SQL {\n\tconst chunks: SQLChunk[] = [sql`substring(`, column];\n\tif (from !== undefined) {\n\t\tchunks.push(sql` from `, bindIfParam(from, column));\n\t}\n\tif (_for !== undefined) {\n\t\tchunks.push(sql` for `, bindIfParam(_for, column));\n\t}\n\tchunks.push(sql`)`);\n\treturn sql.join(chunks);\n}\n\n// Vectors\nexport function dotProduct(column: SingleStoreColumn | SQL.Aliased, value: Array<number>): SQL {\n\treturn sql`${column} <*> ${JSON.stringify(value)}`;\n}\n\nexport function euclideanDistance(column: SingleStoreColumn | SQL.Aliased, value: Array<number>): SQL {\n\treturn sql`${column} <-> ${JSON.stringify(value)}`;\n}\n"], "mappings": "AAAA,SAAS,mBAAmB;AAE5B,SAAS,WAAW;AAGpB,cAAc;AAEP,SAAS,OAAO,QAAyC,OAA+C;AAC9G,SAAO,MAAM,MAAM,OAAO,YAAY,OAAO,MAAM,CAAC;AACrD;AAEO,SAAS,UACf,QACA,EAAE,MAAM,KAAK,KAAK,GACZ;AACN,QAAM,SAAqB,CAAC,iBAAiB,MAAM;AACnD,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,aAAa,YAAY,MAAM,MAAM,CAAC;AAAA,EACnD;AACA,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,YAAY,YAAY,MAAM,MAAM,CAAC;AAAA,EAClD;AACA,SAAO,KAAK,MAAM;AAClB,SAAO,IAAI,KAAK,MAAM;AACvB;AAGO,SAAS,WAAW,QAAyC,OAA2B;AAC9F,SAAO,MAAM,MAAM,QAAQ,KAAK,UAAU,KAAK,CAAC;AACjD;AAEO,SAAS,kBAAkB,QAAyC,OAA2B;AACrG,SAAO,MAAM,MAAM,QAAQ,KAAK,UAAU,KAAK,CAAC;AACjD;", "names": []}
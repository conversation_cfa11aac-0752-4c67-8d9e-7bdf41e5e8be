{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "sourcesContent": ["import type webpack from 'webpack'\nimport { RSC_MODULE_TYPES } from '../../../shared/lib/constants'\nimport { getModuleBuildInfo } from './get-module-build-info'\n\nconst imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'avif', 'ico', 'svg']\nconst imageRegex = new RegExp(`\\\\.(${imageExtensions.join('|')})$`)\n\n// Determine if the whole module is client action, 'use server' in nested closure in the client module\nfunction isActionClientLayerModule(mod: webpack.NormalModule) {\n  const rscInfo = getModuleBuildInfo(mod).rsc\n  return !!(rscInfo?.actionIds && rscInfo?.type === RSC_MODULE_TYPES.client)\n}\n\nexport function isClientComponentEntryModule(mod: webpack.NormalModule) {\n  const rscInfo = getModuleBuildInfo(mod).rsc\n  const hasClientDirective = rscInfo?.isClientRef\n  const isActionLayerEntry = isActionClientLayerModule(mod)\n  return (\n    hasClientDirective || isActionLayerEntry || imageRegex.test(mod.resource)\n  )\n}\n\nexport const regexCSS = /\\.(css|scss|sass)(\\?.*)?$/\n\n// This function checks if a module is able to emit CSS resources. You should\n// never only rely on a single regex to do that.\nexport function isCSSMod(mod: {\n  resource: string\n  type?: string\n  loaders?: { loader: string }[]\n}): boolean {\n  return !!(\n    mod.type === 'css/mini-extract' ||\n    (mod.resource && regexCSS.test(mod.resource)) ||\n    mod.loaders?.some(\n      ({ loader }) =>\n        loader.includes('next-style-loader/index.js') ||\n        (process.env.NEXT_RSPACK &&\n          loader.includes('rspack.CssExtractRspackPlugin.loader')) ||\n        loader.includes('mini-css-extract-plugin/loader.js') ||\n        loader.includes('@vanilla-extract/webpack-plugin/loader/')\n    )\n  )\n}\n\nexport function encodeToBase64<T extends object>(obj: T): string {\n  return Buffer.from(JSON.stringify(obj)).toString('base64')\n}\n\nexport function decodeFromBase64<T extends object>(str: string): T {\n  return JSON.parse(Buffer.from(str, 'base64').toString('utf8'))\n}\n\nexport async function getLoaderModuleNamedExports(\n  resourcePath: string,\n  context: webpack.LoaderContext<any>\n): Promise<string[]> {\n  if (process.env.NEXT_RSPACK) {\n    // Currently, the loadModule method is not supported in Rspack.\n    // Use getModuleNamedExports (implemented by us using SWC) to extract named exports from the module.\n    const binding = require('../../swc') as typeof import('../../swc')\n    return binding.getModuleNamedExports(resourcePath)\n  }\n\n  const mod = await new Promise<webpack.NormalModule>((res, rej) => {\n    context.loadModule(\n      resourcePath,\n      (err: null | Error, _source: any, _sourceMap: any, module: any) => {\n        if (err) {\n          return rej(err)\n        }\n        res(module)\n      }\n    )\n  })\n\n  const exportNames =\n    mod.dependencies\n      ?.filter((dep) => {\n        return (\n          [\n            'HarmonyExportImportedSpecifierDependency',\n            'HarmonyExportSpecifierDependency',\n          ].includes(dep.constructor.name) &&\n          'name' in dep &&\n          dep.name !== 'default'\n        )\n      })\n      .map((dep: any) => {\n        return dep.name\n      }) || []\n  return exportNames\n}\n"], "names": ["decodeFromBase64", "encodeToBase64", "getLoaderModuleNamedExports", "isCSSMod", "isClientComponentEntryModule", "regexCSS", "imageExtensions", "imageRegex", "RegExp", "join", "isActionClientLayerModule", "mod", "rscInfo", "getModuleBuildInfo", "rsc", "actionIds", "type", "RSC_MODULE_TYPES", "client", "hasClientDirective", "isClientRef", "isActionLayerEntry", "test", "resource", "loaders", "some", "loader", "includes", "process", "env", "NEXT_RSPACK", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "str", "parse", "resourcePath", "context", "binding", "require", "getModuleNamedExports", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportNames", "dependencies", "filter", "dep", "constructor", "name", "map"], "mappings": ";;;;;;;;;;;;;;;;;;;IAiDgBA,gBAAgB;eAAhBA;;IAJAC,cAAc;eAAdA;;IAQMC,2BAA2B;eAA3BA;;IA3BNC,QAAQ;eAARA;;IAbAC,4BAA4B;eAA5BA;;IASHC,QAAQ;eAARA;;;2BArBoB;oCACE;AAEnC,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAElE,sGAAsG;AACtG,SAASC,0BAA0BC,GAAyB;IAC1D,MAAMC,UAAUC,IAAAA,sCAAkB,EAACF,KAAKG,GAAG;IAC3C,OAAO,CAAC,CAAEF,CAAAA,CAAAA,2BAAAA,QAASG,SAAS,KAAIH,CAAAA,2BAAAA,QAASI,IAAI,MAAKC,2BAAgB,CAACC,MAAM,AAAD;AAC1E;AAEO,SAASd,6BAA6BO,GAAyB;IACpE,MAAMC,UAAUC,IAAAA,sCAAkB,EAACF,KAAKG,GAAG;IAC3C,MAAMK,qBAAqBP,2BAAAA,QAASQ,WAAW;IAC/C,MAAMC,qBAAqBX,0BAA0BC;IACrD,OACEQ,sBAAsBE,sBAAsBd,WAAWe,IAAI,CAACX,IAAIY,QAAQ;AAE5E;AAEO,MAAMlB,WAAW;AAIjB,SAASF,SAASQ,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAIK,IAAI,KAAK,sBACZL,IAAIY,QAAQ,IAAIlB,SAASiB,IAAI,CAACX,IAAIY,QAAQ,OAC3CZ,eAAAA,IAAIa,OAAO,qBAAXb,aAAac,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCACfC,QAAQC,GAAG,CAACC,WAAW,IACtBJ,OAAOC,QAAQ,CAAC,2CAClBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEO,SAAS1B,eAAiC8B,GAAM;IACrD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEO,SAASpC,iBAAmCqC,GAAW;IAC5D,OAAOH,KAAKI,KAAK,CAACN,OAAOC,IAAI,CAACI,KAAK,UAAUD,QAAQ,CAAC;AACxD;AAEO,eAAelC,4BACpBqC,YAAoB,EACpBC,OAAmC;QAsBjC7B;IApBF,IAAIiB,QAAQC,GAAG,CAACC,WAAW,EAAE;QAC3B,+DAA+D;QAC/D,oGAAoG;QACpG,MAAMW,UAAUC,QAAQ;QACxB,OAAOD,QAAQE,qBAAqB,CAACJ;IACvC;IAEA,MAAM5B,MAAM,MAAM,IAAIiC,QAA8B,CAACC,KAAKC;QACxDN,QAAQO,UAAU,CAChBR,cACA,CAACS,KAAmBC,SAAcC,YAAiBC;YACjD,IAAIH,KAAK;gBACP,OAAOF,IAAIE;YACb;YACAH,IAAIM;QACN;IAEJ;IAEA,MAAMC,cACJzC,EAAAA,oBAAAA,IAAI0C,YAAY,qBAAhB1C,kBACI2C,MAAM,CAAC,CAACC;QACR,OACE;YACE;YACA;SACD,CAAC5B,QAAQ,CAAC4B,IAAIC,WAAW,CAACC,IAAI,KAC/B,UAAUF,OACVA,IAAIE,IAAI,KAAK;IAEjB,GACCC,GAAG,CAAC,CAACH;QACJ,OAAOA,IAAIE,IAAI;IACjB,OAAM,EAAE;IACZ,OAAOL;AACT", "ignoreList": [0]}
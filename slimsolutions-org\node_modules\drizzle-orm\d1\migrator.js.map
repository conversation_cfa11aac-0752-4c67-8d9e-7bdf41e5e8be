{"version": 3, "sources": ["../../src/d1/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { DrizzleD1Database } from './driver.ts';\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: DrizzleD1Database<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tconst migrationsTable = config.migrationsTable ?? '__drizzle_migrations';\n\n\tconst migrationTableCreate = sql`\n\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\tid SERIAL PRIMARY KEY,\n\t\t\thash text NOT NULL,\n\t\t\tcreated_at numeric\n\t\t)\n\t`;\n\tawait db.session.run(migrationTableCreate);\n\n\tconst dbMigrations = await db.values<[number, string, string]>(\n\t\tsql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`,\n\t);\n\n\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\n\tconst statementToBatch = [];\n\n\tfor (const migration of migrations) {\n\t\tif (!lastDbMigration || Number(lastDbMigration[2])! < migration.folderMillis) {\n\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\tstatementToBatch.push(db.run(sql.raw(stmt)));\n\t\t\t}\n\n\t\t\tstatementToBatch.push(\n\t\t\t\tdb.run(\n\t\t\t\t\tsql`INSERT INTO ${sql.identifier(migrationsTable)} (\"hash\", \"created_at\") VALUES(${\n\t\t\t\t\t\tsql.raw(`'${migration.hash}'`)\n\t\t\t\t\t}, ${sql.raw(`${migration.folderMillis}`)})`,\n\t\t\t\t),\n\t\t\t);\n\t\t}\n\t}\n\n\tif (statementToBatch.length > 0) {\n\t\tawait db.session.batch(statementToBatch);\n\t}\n}\n"], "mappings": "AACA,SAAS,0BAA0B;AACnC,SAAS,WAAW;AAGpB,eAAsB,QACrB,IACA,QACC;AACD,QAAM,aAAa,mBAAmB,MAAM;AAC5C,QAAM,kBAAkB,OAAO,mBAAmB;AAElD,QAAM,uBAAuB;AAAA,+BACC,IAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,QAAM,GAAG,QAAQ,IAAI,oBAAoB;AAEzC,QAAM,eAAe,MAAM,GAAG;AAAA,IAC7B,uCAAuC,IAAI,WAAW,eAAe,CAAC;AAAA,EACvE;AAEA,QAAM,kBAAkB,aAAa,CAAC,KAAK;AAE3C,QAAM,mBAAmB,CAAC;AAE1B,aAAW,aAAa,YAAY;AACnC,QAAI,CAAC,mBAAmB,OAAO,gBAAgB,CAAC,CAAC,IAAK,UAAU,cAAc;AAC7E,iBAAW,QAAQ,UAAU,KAAK;AACjC,yBAAiB,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,MAC5C;AAEA,uBAAiB;AAAA,QAChB,GAAG;AAAA,UACF,kBAAkB,IAAI,WAAW,eAAe,CAAC,kCAChD,IAAI,IAAI,IAAI,UAAU,IAAI,GAAG,CAC9B,KAAK,IAAI,IAAI,GAAG,UAAU,YAAY,EAAE,CAAC;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,iBAAiB,SAAS,GAAG;AAChC,UAAM,GAAG,QAAQ,MAAM,gBAAgB;AAAA,EACxC;AACD;", "names": []}
{"version": 3, "sources": ["../../src/mysql-core/alias.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\nimport type { MySqlTable } from './table.ts';\nimport type { MySqlViewBase } from './view-base.ts';\n\nexport function alias<TTable extends MySqlTable | MySqlViewBase, TAlias extends string>(\n\ttable: TTable,\n\talias: T<PERSON>lia<PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n"], "mappings": "AAAA,SAAS,8BAA8B;AAKhC,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,uBAAuBA,QAAO,KAAK,CAAC;AACjE;", "names": ["alias"]}
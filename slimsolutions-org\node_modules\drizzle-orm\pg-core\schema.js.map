{"version": 3, "sources": ["../../src/pg-core/schema.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport type { NonArray, Writable } from '~/utils.ts';\nimport { type PgEnum, type PgEnumObject, pgEnumObjectWithSchema, pgEnumWithSchema } from './columns/enum.ts';\nimport { type pgSequence, pgSequenceWithSchema } from './sequence.ts';\nimport { type PgTableFn, pgTableWithSchema } from './table.ts';\nimport { type pgMaterializedView, pgMaterializedViewWithSchema, type pgView, pgViewWithSchema } from './view.ts';\n\nexport class PgSchema<TName extends string = string> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'PgSchema';\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: PgTableFn<TName> = ((name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t});\n\n\tview = ((name, columns) => {\n\t\treturn pgViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgView;\n\n\tmaterializedView = ((name, columns) => {\n\t\treturn pgMaterializedViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgMaterializedView;\n\n\tpublic enum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\t\tenumName: string,\n\t\tvalues: T | Writable<T>,\n\t): PgEnum<Writable<T>>;\n\n\tpublic enum<E extends Record<string, string>>(\n\t\tenumName: string,\n\t\tenumObj: NonArray<E>,\n\t): PgEnumObject<E>;\n\n\tpublic enum(enumName: any, input: any): any {\n\t\treturn Array.isArray(input)\n\t\t\t? pgEnumWithSchema(\n\t\t\t\tenumName,\n\t\t\t\t[...input] as [string, ...string[]],\n\t\t\t\tthis.schemaName,\n\t\t\t)\n\t\t\t: pgEnumObjectWithSchema(enumName, input, this.schemaName);\n\t}\n\n\tsequence: typeof pgSequence = ((name, options) => {\n\t\treturn pgSequenceWithSchema(name, options, this.schemaName);\n\t});\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([sql.identifier(this.schemaName)]);\n\t}\n\n\tshouldOmitSQLParens(): boolean {\n\t\treturn true;\n\t}\n}\n\nexport function isPgSchema(obj: unknown): obj is PgSchema {\n\treturn is(obj, PgSchema);\n}\n\nexport function pgSchema<T extends string>(name: T) {\n\tif (name === 'public') {\n\t\tthrow new Error(\n\t\t\t`You can't specify 'public' as schema name. Postgres is using public schema by default. If you want to use 'public' schema, just use pgTable() instead of creating a schema`,\n\t\t);\n\t}\n\n\treturn new PgSchema(name);\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAC/B,SAAS,KAAK,WAA4B;AAE1C,SAAyC,wBAAwB,wBAAwB;AACzF,SAA0B,4BAA4B;AACtD,SAAyB,yBAAyB;AAClD,SAAkC,8BAA2C,wBAAwB;AAE9F,MAAM,SAA8D;AAAA,EAE1E,YACiB,YACf;AADe;AAAA,EACd;AAAA,EAHH,QAAiB,UAAU,IAAY;AAAA,EAKvC,QAA2B,CAAC,MAAM,SAAS,gBAAgB;AAC1D,WAAO,kBAAkB,MAAM,SAAS,aAAa,KAAK,UAAU;AAAA,EACrE;AAAA,EAEA,OAAQ,CAAC,MAAM,YAAY;AAC1B,WAAO,iBAAiB,MAAM,SAAS,KAAK,UAAU;AAAA,EACvD;AAAA,EAEA,mBAAoB,CAAC,MAAM,YAAY;AACtC,WAAO,6BAA6B,MAAM,SAAS,KAAK,UAAU;AAAA,EACnE;AAAA,EAYO,KAAK,UAAe,OAAiB;AAC3C,WAAO,MAAM,QAAQ,KAAK,IACvB;AAAA,MACD;AAAA,MACA,CAAC,GAAG,KAAK;AAAA,MACT,KAAK;AAAA,IACN,IACE,uBAAuB,UAAU,OAAO,KAAK,UAAU;AAAA,EAC3D;AAAA,EAEA,WAA+B,CAAC,MAAM,YAAY;AACjD,WAAO,qBAAqB,MAAM,SAAS,KAAK,UAAU;AAAA,EAC3D;AAAA,EAEA,SAAc;AACb,WAAO,IAAI,IAAI,CAAC,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,sBAA+B;AAC9B,WAAO;AAAA,EACR;AACD;AAEO,SAAS,WAAW,KAA+B;AACzD,SAAO,GAAG,KAAK,QAAQ;AACxB;AAEO,SAAS,SAA2B,MAAS;AACnD,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,SAAS,IAAI;AACzB;", "names": []}
{"version": 3, "sources": ["../../../src/sql/expressions/conditions.ts"], "sourcesContent": ["import { type AnyColumn, Column, type GetColumnData } from '~/column.ts';\nimport { is } from '~/entity.ts';\nimport { Table } from '~/table.ts';\nimport {\n\tisDriverValueEncoder,\n\tisSQLWrapper,\n\tParam,\n\tPlaceholder,\n\tSQL,\n\tsql,\n\ttype S<PERSON><PERSON>hunk,\n\ttype SQLWrapper,\n\tStringChunk,\n\tView,\n} from '../sql.ts';\n\nexport function bindIfParam(value: unknown, column: SQLWrapper): SQLChunk {\n\tif (\n\t\tisDriverValueEncoder(column)\n\t\t&& !isSQLWrapper(value)\n\t\t&& !is(value, Param)\n\t\t&& !is(value, Placeholder)\n\t\t&& !is(value, Column)\n\t\t&& !is(value, Table)\n\t\t&& !is(value, View)\n\t) {\n\t\treturn new Param(value, column);\n\t}\n\treturn value as SQLChunk;\n}\n\nexport interface BinaryOperator {\n\t<TColumn extends Column>(\n\t\tleft: TColumn,\n\t\tright: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\t): SQL;\n\t<T>(left: SQL.Aliased<T>, right: T | SQLWrapper): SQL;\n\t<T extends SQLWrapper>(\n\t\tleft: Exclude<T, SQL.Aliased | Column>,\n\t\tright: unknown,\n\t): SQL;\n}\n\n/**\n * Test that two values are equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is null, you may want to use\n * `isNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford\n * db.select().from(cars)\n *   .where(eq(cars.make, 'Ford'))\n * ```\n *\n * @see isNull for a way to test equality to NULL.\n */\nexport const eq: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} = ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that two values are not equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is not null, you may want to use\n * `isNotNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars not made by Ford\n * db.select().from(cars)\n *   .where(ne(cars.make, 'Ford'))\n * ```\n *\n * @see isNotNull for a way to test whether a value is not null.\n */\nexport const ne: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <> ${bindIfParam(right, left)}`;\n};\n\n/**\n * Combine a list of conditions with the `and` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     and(\n *       eq(cars.make, 'Volvo'),\n *       eq(cars.year, 1950),\n *     )\n *   )\n * ```\n */\nexport function and(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function and(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' and ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Combine a list of conditions with the `or` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     or(\n *       eq(cars.make, 'GM'),\n *       eq(cars.make, 'Ford'),\n *     )\n *   )\n * ```\n */\nexport function or(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function or(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' or ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Negate the meaning of an expression using the `not` keyword.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars _not_ made by GM or Ford.\n * db.select().from(cars)\n *   .where(not(inArray(cars.make, ['GM', 'Ford'])))\n * ```\n */\nexport function not(condition: SQLWrapper): SQL {\n\treturn sql`not ${condition}`;\n}\n\n/**\n * Test that the first expression passed is greater than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made after 2000.\n * db.select().from(cars)\n *   .where(gt(cars.year, 2000))\n * ```\n *\n * @see gte for greater-than-or-equal\n */\nexport const gt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} > ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is greater than\n * or equal to the second expression. Use `gt` to\n * test whether an expression is strictly greater\n * than another.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made on or after 2000.\n * db.select().from(cars)\n *   .where(gte(cars.year, 2000))\n * ```\n *\n * @see gt for a strictly greater-than condition\n */\nexport const gte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} >= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lt(cars.year, 2000))\n * ```\n *\n * @see lte for less-than-or-equal\n */\nexport const lt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} < ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * or equal to the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lte(cars.year, 2000))\n * ```\n *\n * @see lt for a strictly less-than condition\n */\nexport const lte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value from a list passed as the second argument.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford or GM.\n * db.select().from(cars)\n *   .where(inArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see notInArray for the inverse of this test\n */\nexport function inArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: ReadonlyArray<GetColumnData<TColumn, 'raw'> | Placeholder> | SQLWrapper,\n): SQL;\nexport function inArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: ReadonlyArray<unknown | Placeholder> | SQLWrapper,\n): SQL;\nexport function inArray(\n\tcolumn: SQLWrapper,\n\tvalues: ReadonlyArray<unknown | Placeholder> | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\treturn sql`false`;\n\t\t}\n\t\treturn sql`${column} in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value that is not present in a list passed as the\n * second argument.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by any company except Ford or GM.\n * db.select().from(cars)\n *   .where(notInArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see inArray for the inverse of this test\n */\nexport function notInArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\treturn sql`true`;\n\t\t}\n\t\treturn sql`${column} not in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} not in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether an expression is NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have no discontinuedAt date.\n * db.select().from(cars)\n *   .where(isNull(cars.discontinuedAt))\n * ```\n *\n * @see isNotNull for the inverse of this test\n */\nexport function isNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is null`;\n}\n\n/**\n * Test whether an expression is not NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have been discontinued.\n * db.select().from(cars)\n *   .where(isNotNull(cars.discontinuedAt))\n * ```\n *\n * @see isNull for the inverse of this test\n */\nexport function isNotNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is not null`;\n}\n\n/**\n * Test whether a subquery evaluates to have any rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column has a match in a cities\n * // table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     exists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see notExists for the inverse of this test\n */\nexport function exists(subquery: SQLWrapper): SQL {\n\treturn sql`exists ${subquery}`;\n}\n\n/**\n * Test whether a subquery doesn't include any result\n * rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column doesn't match\n * // a row in the cities table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     notExists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see exists for the inverse of this test\n */\nexport function notExists(subquery: SQLWrapper): SQL {\n\treturn sql`not exists ${subquery}`;\n}\n\n/**\n * Test whether an expression is between two values. This\n * is an easier way to express range tests, which would be\n * expressed mathematically as `x <= a <= y` but in SQL\n * would have to be like `a >= x AND a <= y`.\n *\n * Between is inclusive of the endpoints: if `column`\n * is equal to `min` or `max`, it will be TRUE.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made between 1990 and 2000\n * db.select().from(cars)\n *   .where(between(cars.year, 1990, 2000))\n * ```\n *\n * @see notBetween for the inverse of this test\n */\nexport function between<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function between<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function between<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function between(column: SQLWrapper, min: unknown, max: unknown): SQL {\n\treturn sql`${column} between ${bindIfParam(min, column)} and ${\n\t\tbindIfParam(\n\t\t\tmax,\n\t\t\tcolumn,\n\t\t)\n\t}`;\n}\n\n/**\n * Test whether an expression is not between two values.\n *\n * This, like `between`, includes its endpoints, so if\n * the `column` is equal to `min` or `max`, in this case\n * it will evaluate to FALSE.\n *\n * ## Examples\n *\n * ```ts\n * // Exclude cars made in the 1970s\n * db.select().from(cars)\n *   .where(notBetween(cars.year, 1970, 1979))\n * ```\n *\n * @see between for the inverse of this test\n */\nexport function notBetween<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function notBetween<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function notBetween<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function notBetween(\n\tcolumn: SQLWrapper,\n\tmin: unknown,\n\tmax: unknown,\n): SQL {\n\treturn sql`${column} not between ${\n\t\tbindIfParam(\n\t\t\tmin,\n\t\t\tcolumn,\n\t\t)\n\t} and ${bindIfParam(max, column)}`;\n}\n\n/**\n * Compare a column to a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(like(cars.name, '%Turbo%'))\n * ```\n *\n * @see ilike for a case-insensitive version of this condition\n */\nexport function like(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} like ${value}`;\n}\n\n/**\n * The inverse of like - this tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"ROver\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see like for the inverse condition\n * @see notIlike for a case-insensitive version of this condition\n */\nexport function notLike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not like ${value}`;\n}\n\n/**\n * Case-insensitively compare a column to a pattern,\n * which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * Unlike like, this performs a case-insensitive comparison.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(ilike(cars.name, '%Turbo%'))\n * ```\n *\n * @see like for a case-sensitive version of this condition\n */\nexport function ilike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} ilike ${value}`;\n}\n\n/**\n * The inverse of ilike - this case-insensitively tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"Rover\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see ilike for the inverse condition\n * @see notLike for a case-sensitive version of this condition\n */\nexport function notIlike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not ilike ${value}`;\n}\n\n/**\n * Test that a column or expression contains all elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\" and \"ORM\".\n * db.select().from(posts)\n *   .where(arrayContains(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContained to find if an array contains all elements of a column or expression\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContains<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContains(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContains requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} @> ${array}`;\n\t}\n\n\treturn sql`${column} @> ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that the list passed as the second argument contains\n * all elements of a column or expression.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both,\n * // but filtering posts that have additional tags.\n * db.select().from(posts)\n *   .where(arrayContained(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContained<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContained(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContained requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} <@ ${array}`;\n\t}\n\n\treturn sql`${column} <@ ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that a column or expression contains any elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both.\n * db.select().from(posts)\n *   .where(arrayOverlaps(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayContained to find if an array contains all elements of a column or expression\n */\nexport function arrayOverlaps<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayOverlaps(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayOverlaps requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} && ${array}`;\n\t}\n\n\treturn sql`${column} && ${bindIfParam(values, column)}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2D;AAC3D,oBAAmB;AACnB,mBAAsB;AACtB,iBAWO;AAEA,SAAS,YAAY,OAAgB,QAA8B;AACzE,UACC,iCAAqB,MAAM,KACxB,KAAC,yBAAa,KAAK,KACnB,KAAC,kBAAG,OAAO,gBAAK,KAChB,KAAC,kBAAG,OAAO,sBAAW,KACtB,KAAC,kBAAG,OAAO,oBAAM,KACjB,KAAC,kBAAG,OAAO,kBAAK,KAChB,KAAC,kBAAG,OAAO,eAAI,GACjB;AACD,WAAO,IAAI,iBAAM,OAAO,MAAM;AAAA,EAC/B;AACA,SAAO;AACR;AAgCO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,iBAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAoBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,iBAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AAmBO,SAAS,OACZ,sBACe;AAClB,QAAM,aAAa,qBAAqB;AAAA,IACvC,CAAC,MAAyC,MAAM;AAAA,EACjD;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO;AAAA,EACR;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO,IAAI,eAAI,UAAU;AAAA,EAC1B;AAEA,SAAO,IAAI,eAAI;AAAA,IACd,IAAI,uBAAY,GAAG;AAAA,IACnB,eAAI,KAAK,YAAY,IAAI,uBAAY,OAAO,CAAC;AAAA,IAC7C,IAAI,uBAAY,GAAG;AAAA,EACpB,CAAC;AACF;AAmBO,SAAS,MACZ,sBACe;AAClB,QAAM,aAAa,qBAAqB;AAAA,IACvC,CAAC,MAAyC,MAAM;AAAA,EACjD;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO;AAAA,EACR;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO,IAAI,eAAI,UAAU;AAAA,EAC1B;AAEA,SAAO,IAAI,eAAI;AAAA,IACd,IAAI,uBAAY,GAAG;AAAA,IACnB,eAAI,KAAK,YAAY,IAAI,uBAAY,MAAM,CAAC;AAAA,IAC5C,IAAI,uBAAY,GAAG;AAAA,EACpB,CAAC;AACF;AAaO,SAAS,IAAI,WAA4B;AAC/C,SAAO,qBAAU,SAAS;AAC3B;AAgBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,iBAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAkBO,MAAM,MAAsB,CAAC,MAAkB,UAAwB;AAC7E,SAAO,iBAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AAgBO,MAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,iBAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAgBO,MAAM,MAAsB,CAAC,MAAkB,UAAwB;AAC7E,SAAO,iBAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AA4BO,SAAS,QACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,aAAO;AAAA,IACR;AACA,WAAO,iBAAM,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO,iBAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AA6BO,SAAS,WACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,aAAO;AAAA,IACR;AACA,WAAO,iBAAM,MAAM,WAAW,OAAO,IAAI,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC;AAAA,EACxE;AAEA,SAAO,iBAAM,MAAM,WAAW,YAAY,QAAQ,MAAM,CAAC;AAC1D;AAkBO,SAAS,OAAO,OAAwB;AAC9C,SAAO,iBAAM,KAAK;AACnB;AAkBO,SAAS,UAAU,OAAwB;AACjD,SAAO,iBAAM,KAAK;AACnB;AAsBO,SAAS,OAAO,UAA2B;AACjD,SAAO,wBAAa,QAAQ;AAC7B;AAuBO,SAAS,UAAU,UAA2B;AACpD,SAAO,4BAAiB,QAAQ;AACjC;AAoCO,SAAS,QAAQ,QAAoB,KAAc,KAAmB;AAC5E,SAAO,iBAAM,MAAM,YAAY,YAAY,KAAK,MAAM,CAAC,QACtD;AAAA,IACC;AAAA,IACA;AAAA,EACD,CACD;AACD;AAkCO,SAAS,WACf,QACA,KACA,KACM;AACN,SAAO,iBAAM,MAAM,gBAClB;AAAA,IACC;AAAA,IACA;AAAA,EACD,CACD,QAAQ,YAAY,KAAK,MAAM,CAAC;AACjC;AAkBO,SAAS,KAAK,QAAoC,OAAiC;AACzF,SAAO,iBAAM,MAAM,SAAS,KAAK;AAClC;AAoBO,SAAS,QAAQ,QAAoC,OAAiC;AAC5F,SAAO,iBAAM,MAAM,aAAa,KAAK;AACtC;AAqBO,SAAS,MAAM,QAAoC,OAAiC;AAC1F,SAAO,iBAAM,MAAM,UAAU,KAAK;AACnC;AAoBO,SAAS,SAAS,QAAoC,OAAiC;AAC7F,SAAO,iBAAM,MAAM,cAAc,KAAK;AACvC;AAkCO,SAAS,cACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AACA,UAAM,QAAQ,iBAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,iBAAM,MAAM,OAAO,KAAK;AAAA,EAChC;AAEA,SAAO,iBAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AAmCO,SAAS,eACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC7D;AACA,UAAM,QAAQ,iBAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,iBAAM,MAAM,OAAO,KAAK;AAAA,EAChC;AAEA,SAAO,iBAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AAkCO,SAAS,cACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AACA,UAAM,QAAQ,iBAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,iBAAM,MAAM,OAAO,KAAK;AAAA,EAChC;AAEA,SAAO,iBAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;", "names": []}
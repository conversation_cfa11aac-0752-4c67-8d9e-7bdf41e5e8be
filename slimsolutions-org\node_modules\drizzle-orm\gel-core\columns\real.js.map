{"version": 3, "sources": ["../../../src/gel-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelRealBuilderInitial<TName extends string> = GelRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'GelReal';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class GelRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'GelReal'>> extends GelColumnBuilder<\n\tT,\n\t{ length: number | undefined }\n> {\n\tstatic override readonly [entityKind]: string = 'GelRealBuilder';\n\n\tconstructor(name: T['name'], length?: number) {\n\t\tsuper(name, 'number', 'GelReal');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelReal<T extends ColumnBaseConfig<'number', 'GelReal'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelReal';\n\n\tconstructor(table: AnyGelTable<{ name: T['tableName'] }>, config: GelRealBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'real';\n\t}\n}\n\nexport function real(): GelRealBuilderInitial<''>;\nexport function real<TName extends string>(name: TName): GelRealBuilderInitial<TName>;\nexport function real(name?: string) {\n\treturn new GelRealBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,uBAA+E,iBAG1F;AAAA,EACD,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAiB;AAC7C,UAAM,MAAM,UAAU,SAAS;AAC/B,SAAK,OAAO,SAAS;AAAA,EACtB;AAAA;AAAA,EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;AAAA,EAC/G;AACD;AAEO,MAAM,gBAAiE,UAAa;AAAA,EAC1F,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,OAA8C,QAAqC;AAC9F,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,eAAe,QAAQ,EAAE;AACrC;", "names": []}
{"version": 3, "sources": ["../../src/singlestore-core/subquery.ts"], "sourcesContent": ["import type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport type { Subquery, WithSubquery, WithSubqueryWithoutSelection } from '~/subquery.ts';\nimport type { QueryBuilder } from './query-builders/query-builder.ts';\n\nexport type SubqueryWithSelection<\n\tTSelection extends ColumnsSelection,\n\tT<PERSON><PERSON><PERSON> extends string,\n> =\n\t& Subquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'singlestore'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'singlestore'>;\n\nexport type WithSubqueryWithSelection<\n\tTSelection extends ColumnsSelection,\n\tTAlias extends string,\n> =\n\t& WithSubquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'singlestore'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'singlestore'>;\n\nexport interface WithBuilder {\n\t<TAlias extends string>(alias: T<PERSON><PERSON><PERSON>): {\n\t\tas: {\n\t\t\t<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t(\n\t\t\t\tqb: TypedQueryBuilder<undefined> | ((qb: QueryBuilder) => TypedQueryBuilder<undefined>),\n\t\t\t): WithSubqueryWithoutSelection<TAlias>;\n\t\t};\n\t};\n\t<TAlias extends string, TSelection extends ColumnsSelection>(alias: TAlias, selection: TSelection): {\n\t\tas: (qb: SQL | ((qb: QueryBuilder) => SQL)) => WithSubqueryWithSelection<TSelection, TAlias>;\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}
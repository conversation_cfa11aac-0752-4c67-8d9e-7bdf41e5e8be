{"version": 3, "sources": ["../../src/mysql-core/primary-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnyMySqlColumn, MySqlColumn } from './columns/index.ts';\nimport { MySqlTable } from './table.ts';\n\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumn extends AnyMySqlColumn<{ tableName: TTableName }>,\n\tTColumns extends AnyMySqlColumn<{ tableName: TTableName }>[],\n>(config: { name?: string; columns: [TColumn, ...TColumns] }): PrimaryKeyBuilder;\n/**\n * @deprecated: Please use primaryKey({ columns: [] }) instead of this function\n * @param columns\n */\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumns extends AnyMySqlColumn<{ tableName: TTableName }>[],\n>(...columns: TColumns): PrimaryKeyBuilder;\nexport function primaryKey(...config: any) {\n\tif (config[0].columns) {\n\t\treturn new PrimaryKeyBuilder(config[0].columns, config[0].name);\n\t}\n\treturn new PrimaryKeyBuilder(config);\n}\n\nexport class PrimaryKeyBuilder {\n\tstatic readonly [entityKind]: string = 'MySqlPrimaryKeyBuilder';\n\n\t/** @internal */\n\tcolumns: MySqlColumn[];\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tcolumns: MySqlColumn[],\n\t\tname?: string,\n\t) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\t/** @internal */\n\tbuild(table: MySqlTable): PrimaryKey {\n\t\treturn new PrimaryKey(table, this.columns, this.name);\n\t}\n}\n\nexport class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'MySqlPrimaryKey';\n\n\treadonly columns: MySqlColumn[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: MySqlTable, columns: MySqlColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\tgetName(): string {\n\t\treturn this.name\n\t\t\t?? `${this.table[MySqlTable.Symbol.Name]}_${this.columns.map((column) => column.name).join('_')}_pk`;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,mBAA2B;AAepB,SAAS,cAAc,QAAa;AAC1C,MAAI,OAAO,CAAC,EAAE,SAAS;AACtB,WAAO,IAAI,kBAAkB,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,IAAI;AAAA,EAC/D;AACA,SAAO,IAAI,kBAAkB,MAAM;AACpC;AAEO,MAAM,kBAAkB;AAAA,EAC9B,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGA;AAAA,EAEA,YACC,SACA,MACC;AACD,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAGA,MAAM,OAA+B;AACpC,WAAO,IAAI,WAAW,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EACrD;AACD;AAEO,MAAM,WAAW;AAAA,EAMvB,YAAqB,OAAmB,SAAwB,MAAe;AAA1D;AACpB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA,EARA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAOT,UAAkB;AACjB,WAAO,KAAK,QACR,GAAG,KAAK,MAAM,wBAAW,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EACjG;AACD;", "names": []}
{"version": 3, "sources": ["../../../src/sqlite-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteNumericBuilderInitial<TName extends string> = SQLiteNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'SQLiteNumeric'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SQLiteNumeric');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumeric<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumeric<T extends ColumnBaseConfig<'string', 'SQLiteNumeric'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumeric';\n\n\toverride mapFromDriverValue(value: unknown): string {\n\t\tif (typeof value === 'string') return value;\n\n\t\treturn String(value);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericNumberBuilderInitial<TName extends string> = SQLiteNumericNumberBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteNumericNumber';\n\tdata: number;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericNumberBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteNumericNumber'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericNumberBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteNumericNumber');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumericNumber<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumericNumber<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumericNumber<T extends ColumnBaseConfig<'number', 'SQLiteNumericNumber'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericNumber';\n\n\toverride mapFromDriverValue(value: unknown): number {\n\t\tif (typeof value === 'number') return value;\n\n\t\treturn Number(value);\n\t}\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericBigIntBuilderInitial<TName extends string> = SQLiteNumericBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SQLiteNumericBigInt';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericBigIntBuilder<T extends ColumnBuilderBaseConfig<'bigint', 'SQLiteNumericBigInt'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBigIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'SQLiteNumericBigInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumericBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumericBigInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumericBigInt<T extends ColumnBaseConfig<'bigint', 'SQLiteNumericBigInt'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBigInt';\n\n\toverride mapFromDriverValue = BigInt;\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericConfig<T extends 'string' | 'number' | 'bigint' = 'string' | 'number' | 'bigint'> = {\n\tmode: T;\n};\n\nexport function numeric<TMode extends SQLiteNumericConfig['mode']>(\n\tconfig?: SQLiteNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SQLiteNumericNumberBuilderInitial<''>\n\t: Equal<TMode, 'bigint'> extends true ? SQLiteNumericBigIntBuilderInitial<''>\n\t: SQLiteNumericBuilderInitial<''>;\nexport function numeric<TName extends string, TMode extends SQLiteNumericConfig['mode']>(\n\tname: TName,\n\tconfig?: SQLiteNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SQLiteNumericNumberBuilderInitial<TName>\n\t: Equal<TMode, 'bigint'> extends true ? SQLiteNumericBigIntBuilderInitial<TName>\n\t: SQLiteNumericBuilderInitial<TName>;\nexport function numeric(a?: string | SQLiteNumericConfig, b?: SQLiteNumericConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SQLiteNumericConfig>(a, b);\n\tconst mode = config?.mode;\n\treturn mode === 'number'\n\t\t? new SQLiteNumericNumberBuilder(name)\n\t\t: mode === 'bigint'\n\t\t? new SQLiteNumericBigIntBuilder(name)\n\t\t: new SQLiteNumericBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA8B;AACnD,SAAS,cAAc,2BAA2B;AAW3C,MAAM,6BACJ,oBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,eAAe;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA6E,aAAgB;AAAA,EACzG,QAA0B,UAAU,IAAY;AAAA,EAEvC,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAWO,MAAM,mCACJ,oBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BAAyF,aAAgB;AAAA,EACrH,QAA0B,UAAU,IAAY;AAAA,EAEvC,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAES,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAWO,MAAM,mCACJ,oBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BAAyF,aAAgB;AAAA,EACrH,QAA0B,UAAU,IAAY;AAAA,EAEvC,qBAAqB;AAAA,EAErB,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAiBO,SAAS,QAAQ,GAAkC,GAAyB;AAClF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA4C,GAAG,CAAC;AACzE,QAAM,OAAO,QAAQ;AACrB,SAAO,SAAS,WACb,IAAI,2BAA2B,IAAI,IACnC,SAAS,WACT,IAAI,2BAA2B,IAAI,IACnC,IAAI,qBAAqB,IAAI;AACjC;", "names": []}
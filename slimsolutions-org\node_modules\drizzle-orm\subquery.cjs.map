{"version": 3, "sources": ["../src/subquery.ts"], "sourcesContent": ["import { entityKind } from './entity.ts';\nimport type { SQL, SQLWrapper } from './sql/sql.ts';\n\nexport interface Subquery<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTAlias extends string = string,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\nexport class Subquery<\n\tTAlias extends string = string,\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Subquery';\n\n\tdeclare _: {\n\t\tbrand: 'Subquery';\n\t\tsql: SQL;\n\t\tselectedFields: TSelectedFields;\n\t\talias: TAlias;\n\t\tisWith: boolean;\n\t\tusedTables?: string[];\n\t};\n\n\tconstructor(sql: SQL, fields: TSelectedFields, alias: string, isWith = false, usedTables: string[] = []) {\n\t\tthis._ = {\n\t\t\tbrand: 'Subquery',\n\t\t\tsql,\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t\talias: alias as T<PERSON><PERSON><PERSON>,\n\t\t\tisWith,\n\t\t\tusedTables,\n\t\t};\n\t}\n\n\t// getSQL(): SQL<unknown> {\n\t// \treturn new SQL([this]);\n\t// }\n}\n\nexport class WithSubquery<\n\tTAlias extends string = string,\n\tTSelection extends Record<string, unknown> = Record<string, unknown>,\n> extends Subquery<TAlias, TSelection> {\n\tstatic override readonly [entityKind]: string = 'WithSubquery';\n}\n\nexport type WithSubqueryWithoutSelection<TAlias extends string> = WithSubquery<TAlias, {}>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAWpB,MAAM,SAGW;AAAA,EACvB,QAAiB,wBAAU,IAAY;AAAA,EAWvC,YAAY,KAAU,QAAyB,OAAe,SAAS,OAAO,aAAuB,CAAC,GAAG;AACxG,SAAK,IAAI;AAAA,MACR,OAAO;AAAA,MACP;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAKD;AAEO,MAAM,qBAGH,SAA6B;AAAA,EACtC,QAA0B,wBAAU,IAAY;AACjD;", "names": []}
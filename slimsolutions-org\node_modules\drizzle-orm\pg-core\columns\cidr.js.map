{"version": 3, "sources": ["../../../src/pg-core/columns/cidr.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgCidrBuilderInitial<TName extends string> = PgCidrBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgCidr';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgCidrBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgCidr'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgCidrBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgCidr');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgCidr<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgCidr<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgCidr<T extends ColumnBaseConfig<'string', 'PgCidr'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgCidr';\n\n\tgetSQLType(): string {\n\t\treturn 'cidr';\n\t}\n}\n\nexport function cidr(): PgCidrBuilderInitial<''>;\nexport function cidr<TName extends string>(name: TName): PgCidrBuilderInitial<TName>;\nexport function cidr(name?: string) {\n\treturn new PgCidrBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;AAWnC,MAAM,sBAA6E,gBAAmB;AAAA,EAC5G,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eAA+D,SAAY;AAAA,EACvF,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,cAAc,QAAQ,EAAE;AACpC;", "names": []}
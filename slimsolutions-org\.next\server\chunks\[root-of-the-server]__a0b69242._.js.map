{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/app/api/test-db/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { Pool } from 'pg';\n\nexport async function GET() {\n  try {\n    console.log('Testing database connection...');\n    console.log('Host:', process.env.POSTGRES_HOST);\n    console.log('Port:', process.env.POSTGRES_PORT);\n    console.log('Database:', process.env.POSTGRES_DATABASE);\n    console.log('User:', process.env.POSTGRES_USER);\n    \n    const pool = new Pool({\n      host: process.env.POSTGRES_HOST,\n      port: parseInt(process.env.POSTGRES_PORT || '5432'),\n      user: process.env.POSTGRES_USER,\n      password: process.env.POSTGRES_PASSWORD,\n      database: process.env.POSTGRES_DATABASE,\n      ssl: { rejectUnauthorized: false }\n    });\n\n    const client = await pool.connect();\n    const result = await client.query('SELECT NOW() as current_time');\n    \n    // Test if newsletter_subscriptions table exists\n    const tableCheck = await client.query(`\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_name = 'newsletter_subscriptions'\n      );\n    `);\n    \n    client.release();\n    await pool.end();\n\n    return NextResponse.json({\n      success: true,\n      message: 'Database connection successful',\n      currentTime: result.rows[0].current_time,\n      tableExists: tableCheck.rows[0].exists,\n      env: {\n        host: process.env.POSTGRES_HOST,\n        port: process.env.POSTGRES_PORT,\n        database: process.env.POSTGRES_DATABASE,\n        user: process.env.POSTGRES_USER,\n      }\n    });\n  } catch (error) {\n    console.error('Database test error:', error);\n    return NextResponse.json(\n      { \n        error: 'Database connection failed',\n        details: error.message,\n        env: {\n          host: process.env.POSTGRES_HOST,\n          port: process.env.POSTGRES_PORT,\n          database: process.env.POSTGRES_DATABASE,\n          user: process.env.POSTGRES_USER,\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,aAAa;QAC9C,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,aAAa;QAC9C,QAAQ,GAAG,CAAC,aAAa,QAAQ,GAAG,CAAC,iBAAiB;QACtD,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,aAAa;QAE9C,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;YACpB,MAAM,QAAQ,GAAG,CAAC,aAAa;YAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;YAC5C,MAAM,QAAQ,GAAG,CAAC,aAAa;YAC/B,UAAU,QAAQ,GAAG,CAAC,iBAAiB;YACvC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;YACvC,KAAK;gBAAE,oBAAoB;YAAM;QACnC;QAEA,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;QAElC,gDAAgD;QAChD,MAAM,aAAa,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;;IAMvC,CAAC;QAED,OAAO,OAAO;QACd,MAAM,KAAK,GAAG;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,aAAa,OAAO,IAAI,CAAC,EAAE,CAAC,YAAY;YACxC,aAAa,WAAW,IAAI,CAAC,EAAE,CAAC,MAAM;YACtC,KAAK;gBACH,MAAM,QAAQ,GAAG,CAAC,aAAa;gBAC/B,MAAM,QAAQ,GAAG,CAAC,aAAa;gBAC/B,UAAU,QAAQ,GAAG,CAAC,iBAAiB;gBACvC,MAAM,QAAQ,GAAG,CAAC,aAAa;YACjC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,MAAM,OAAO;YACtB,KAAK;gBACH,MAAM,QAAQ,GAAG,CAAC,aAAa;gBAC/B,MAAM,QAAQ,GAAG,CAAC,aAAa;gBAC/B,UAAU,QAAQ,GAAG,CAAC,iBAAiB;gBACvC,MAAM,QAAQ,GAAG,CAAC,aAAa;YACjC;QACF,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": ["../../src/sqlite-core/primary-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnySQLiteColumn, SQLiteColumn } from './columns/index.ts';\nimport { SQLiteTable } from './table.ts';\n\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumn extends AnySQLiteColumn<{ tableName: TTableName }>,\n\tTColumns extends AnySQLiteColumn<{ tableName: TTableName }>[],\n>(config: { name?: string; columns: [TColumn, ...TColumns] }): PrimaryKeyBuilder;\n/**\n * @deprecated: Please use primaryKey({ columns: [] }) instead of this function\n * @param columns\n */\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumns extends AnySQLiteColumn<{ tableName: TTableName }>[],\n>(...columns: TColumns): PrimaryKeyBuilder;\nexport function primaryKey(...config: any) {\n\tif (config[0].columns) {\n\t\treturn new PrimaryKeyBuilder(config[0].columns, config[0].name);\n\t}\n\treturn new PrimaryKeyBuilder(config);\n}\nexport class PrimaryKeyBuilder {\n\tstatic readonly [entityKind]: string = 'SQLitePrimaryKeyBuilder';\n\n\tdeclare _: {\n\t\tbrand: 'SQLitePrimaryKeyBuilder';\n\t};\n\n\t/** @internal */\n\tcolumns: SQLiteColumn[];\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tcolumns: SQLiteColumn[],\n\t\tname?: string,\n\t) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\t/** @internal */\n\tbuild(table: SQLiteTable): PrimaryKey {\n\t\treturn new PrimaryKey(table, this.columns, this.name);\n\t}\n}\n\nexport class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'SQLitePrimaryKey';\n\n\treadonly columns: SQLiteColumn[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: SQLiteTable, columns: SQLiteColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\tgetName(): string {\n\t\treturn this.name\n\t\t\t?? `${this.table[SQLiteTable.Symbol.Name]}_${this.columns.map((column) => column.name).join('_')}_pk`;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,mBAAmB;AAerB,SAAS,cAAc,QAAa;AAC1C,MAAI,OAAO,CAAC,EAAE,SAAS;AACtB,WAAO,IAAI,kBAAkB,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,IAAI;AAAA,EAC/D;AACA,SAAO,IAAI,kBAAkB,MAAM;AACpC;AACO,MAAM,kBAAkB;AAAA,EAC9B,QAAiB,UAAU,IAAY;AAAA;AAAA,EAOvC;AAAA;AAAA,EAGA;AAAA,EAEA,YACC,SACA,MACC;AACD,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAGA,MAAM,OAAgC;AACrC,WAAO,IAAI,WAAW,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EACrD;AACD;AAEO,MAAM,WAAW;AAAA,EAMvB,YAAqB,OAAoB,SAAyB,MAAe;AAA5D;AACpB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA,EARA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAOT,UAAkB;AACjB,WAAO,KAAK,QACR,GAAG,KAAK,MAAM,YAAY,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAClG;AACD;", "names": []}
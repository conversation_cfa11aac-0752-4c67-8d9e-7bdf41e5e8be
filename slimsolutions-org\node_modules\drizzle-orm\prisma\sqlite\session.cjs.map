{"version": 3, "sources": ["../../../src/prisma/sqlite/session.ts"], "sourcesContent": ["import type { PrismaClient } from '@prisma/client/extension';\n\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport type { Query } from '~/sql/sql.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSelectedFieldsOrdered,\n\tSQLiteAsyncDialect,\n\tSQLiteExecuteMethod,\n\tSQLiteTransaction,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/index.ts';\nimport { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/index.ts';\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class PrismaSQLitePreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'async'; run: []; all: T['all']; get: T['get']; values: never; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'PrismaSQLitePreparedQuery';\n\n\tconstructor(\n\t\tprivate readonly prisma: PrismaClient,\n\t\tquery: Query,\n\t\tprivate readonly logger: Logger,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t) {\n\t\tsuper('async', executeMethod, query);\n\t}\n\n\toverride all(placeholderValues?: Record<string, unknown>): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.prisma.$queryRawUnsafe(this.query.sql, ...params);\n\t}\n\n\toverride async run(placeholderValues?: Record<string, unknown> | undefined): Promise<[]> {\n\t\tawait this.all(placeholderValues);\n\t\treturn [];\n\t}\n\n\toverride async get(placeholderValues?: Record<string, unknown> | undefined): Promise<T['get']> {\n\t\tconst all = await this.all(placeholderValues) as unknown[];\n\t\treturn all[0];\n\t}\n\n\toverride values(_placeholderValues?: Record<string, unknown> | undefined): Promise<never> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\n\toverride isResponseInArrayMode(): boolean {\n\t\treturn false;\n\t}\n}\n\nexport interface PrismaSQLiteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PrismaSQLiteSession extends SQLiteSession<'async', unknown, Record<string, never>, Record<string, never>> {\n\tstatic override readonly [entityKind]: string = 'PrismaSQLiteSession';\n\n\tprivate readonly logger: Logger;\n\n\tconstructor(\n\t\tprivate readonly prisma: PrismaClient,\n\t\tdialect: SQLiteAsyncDialect,\n\t\toptions: PrismaSQLiteSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\toverride prepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t): PrismaSQLitePreparedQuery<T> {\n\t\treturn new PrismaSQLitePreparedQuery(this.prisma, query, this.logger, executeMethod);\n\t}\n\n\toverride transaction<T>(\n\t\t_transaction: (tx: SQLiteTransaction<'async', unknown, Record<string, never>, Record<string, never>>) => Promise<T>,\n\t\t_config?: SQLiteTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAC3B,oBAAwC;AAExC,iBAAiC;AASjC,yBAAmD;AAI5C,MAAM,kCAAuF,uCAElG;AAAA,EAGD,YACkB,QACjB,OACiB,QACjB,eACC;AACD,UAAM,SAAS,eAAe,KAAK;AALlB;AAEA;AAAA,EAIlB;AAAA,EATA,QAA0B,wBAAU,IAAY;AAAA,EAWvC,IAAI,mBAAgE;AAC5E,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,gBAAgB,KAAK,MAAM,KAAK,GAAG,MAAM;AAAA,EAC7D;AAAA,EAEA,MAAe,IAAI,mBAAsE;AACxF,UAAM,KAAK,IAAI,iBAAiB;AAChC,WAAO,CAAC;AAAA,EACT;AAAA,EAEA,MAAe,IAAI,mBAA4E;AAC9F,UAAM,MAAM,MAAM,KAAK,IAAI,iBAAiB;AAC5C,WAAO,IAAI,CAAC;AAAA,EACb;AAAA,EAES,OAAO,oBAA0E;AACzF,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EAES,wBAAiC;AACzC,WAAO;AAAA,EACR;AACD;AAMO,MAAM,4BAA4B,iCAA8E;AAAA,EAKtH,YACkB,QACjB,SACA,SACC;AACD,UAAM,OAAO;AAJI;AAKjB,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAXA,QAA0B,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAWR,aACR,OACA,QACA,eAC+B;AAC/B,WAAO,IAAI,0BAA0B,KAAK,QAAQ,OAAO,KAAK,QAAQ,aAAa;AAAA,EACpF;AAAA,EAES,YACR,cACA,SACa;AACb,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AACD;", "names": []}
{"version": 3, "sources": ["../../src/mysql-proxy/session.ts"], "sourcesContent": ["import type { FieldPacket, ResultSetHeader } from 'mysql2/promise';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport { MySqlTransaction } from '~/mysql-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/mysql-core/query-builders/select.types.ts';\nimport type {\n\tMySqlPreparedQueryConfig,\n\tMySqlPreparedQueryHKT,\n\tMySqlQueryResultHKT,\n\tMySqlTransactionConfig,\n\tPreparedQueryKind,\n} from '~/mysql-core/session.ts';\nimport { MySqlPreparedQuery as PreparedQueryBase, MySqlSession } from '~/mysql-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\nimport type { RemoteCallback } from './driver.ts';\n\nexport type MySqlRawQueryResult = [ResultSetHeader, FieldPacket[]];\n\nexport interface MySqlRemoteSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class MySqlRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlSession<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'MySqlRemoteSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: MySqlDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: MySqlRemoteSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends MySqlPreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PreparedQueryKind<MySqlRemotePreparedQueryHKT, T> {\n\t\treturn new PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t\tgeneratedIds,\n\t\t\treturningIds,\n\t\t) as PreparedQueryKind<MySqlRemotePreparedQueryHKT, T>;\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\t\treturn this.client(querySql.sql, querySql.params, 'all').then(({ rows }) => rows) as Promise<T[]>;\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: MySqlProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t_config?: MySqlTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the MySql Proxy driver');\n\t}\n}\n\nexport class MySqlProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlTransaction<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'MySqlProxyTransaction';\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: MySqlProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the MySql Proxy driver');\n\t}\n}\n\nexport class PreparedQuery<T extends MySqlPreparedQueryConfig> extends PreparedQueryBase<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlProxyPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\t// Keys that were used in $default and the value that was generated for them\n\t\tprivate generatedIds?: Record<string, unknown>[],\n\t\t// Keys that should be returned, it has the column with all properries + key from object\n\t\tprivate returningIds?: SelectedFieldsOrdered,\n\t) {\n\t\tsuper(cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tconst { fields, client, queryString, logger, joinsNotNullableMap, customResultMapper, returningIds, generatedIds } =\n\t\t\tthis;\n\n\t\tlogger.logQuery(queryString, params);\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst { rows: data } = await this.queryWithCache(queryString, params, async () => {\n\t\t\t\treturn await client(queryString, params, 'execute');\n\t\t\t});\n\n\t\t\tconst insertId = data[0].insertId as number;\n\t\t\tconst affectedRows = data[0].affectedRows;\n\n\t\t\tif (returningIds) {\n\t\t\t\tconst returningResponse = [];\n\t\t\t\tlet j = 0;\n\t\t\t\tfor (let i = insertId; i < insertId + affectedRows; i++) {\n\t\t\t\t\tfor (const column of returningIds) {\n\t\t\t\t\t\tconst key = returningIds[0]!.path[0]!;\n\t\t\t\t\t\tif (is(column.field, Column)) {\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\tif (column.field.primary && column.field.autoIncrement) {\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: i });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (column.field.defaultFn && generatedIds) {\n\t\t\t\t\t\t\t\t// generatedIds[rowIdx][key]\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: generatedIds[j]![key] });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tj++;\n\t\t\t\t}\n\n\t\t\t\treturn returningResponse;\n\t\t\t}\n\n\t\t\treturn data;\n\t\t}\n\n\t\tconst { rows } = await this.queryWithCache(queryString, params, async () => {\n\t\t\treturn await client(queryString, params, 'all');\n\t\t});\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\toverride iterator(\n\t\t_placeholderValues: Record<string, unknown> = {},\n\t): AsyncGenerator<T['iterator']> {\n\t\tthrow new Error('Streaming is not supported by the MySql Proxy driver');\n\t}\n}\n\nexport interface MySqlRemoteQueryResultHKT extends MySqlQueryResultHKT {\n\ttype: MySqlRawQueryResult;\n}\n\nexport interface MySqlRemotePreparedQueryHKT extends MySqlPreparedQueryHKT {\n\ttype: PreparedQuery<Assume<this['config'], MySqlPreparedQueryConfig>>;\n}\n"], "mappings": "AACA,SAAqB,iBAAiB;AAEtC,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAE/B,SAAS,kBAAkB;AAE3B,SAAS,wBAAwB;AASjC,SAAS,sBAAsB,mBAAmB,oBAAoB;AAEtE,SAAS,wBAAwB;AAEjC,SAAsB,oBAAoB;AAUnC,MAAM,2BAGH,aAA2F;AAAA,EAMpG,YACS,QACR,SACQ,QACR,SACC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,oBACA,cACA,cACA,eAIA,aACoD;AACpD,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAClD,WAAO,KAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI;AAAA,EACjF;AAAA,EAEA,MAAe,YACd,cACA,SACa;AACb,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AACD;AAEO,MAAM,8BAGH,iBAA+F;AAAA,EACxG,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YACd,cACa;AACb,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AACD;AAEO,MAAM,sBAA0D,kBAAqB;AAAA,EAG3F,YACS,QACA,aACA,QACA,QACR,OACA,eAIA,aACQ,QACA,oBAEA,cAEA,cACP;AACD,UAAM,OAAO,eAAe,WAAW;AAjB/B;AACA;AACA;AACA;AAOA;AACA;AAEA;AAEA;AAAA,EAGT;AAAA,EArBA,QAA0B,UAAU,IAAY;AAAA,EAuBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,UAAM,EAAE,QAAQ,QAAQ,aAAa,QAAQ,qBAAqB,oBAAoB,cAAc,aAAa,IAChH;AAED,WAAO,SAAS,aAAa,MAAM;AAEnC,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AACjF,eAAO,MAAM,OAAO,aAAa,QAAQ,SAAS;AAAA,MACnD,CAAC;AAED,YAAM,WAAW,KAAK,CAAC,EAAE;AACzB,YAAM,eAAe,KAAK,CAAC,EAAE;AAE7B,UAAI,cAAc;AACjB,cAAM,oBAAoB,CAAC;AAC3B,YAAI,IAAI;AACR,iBAAS,IAAI,UAAU,IAAI,WAAW,cAAc,KAAK;AACxD,qBAAW,UAAU,cAAc;AAClC,kBAAM,MAAM,aAAa,CAAC,EAAG,KAAK,CAAC;AACnC,gBAAI,GAAG,OAAO,OAAO,MAAM,GAAG;AAE7B,kBAAI,OAAO,MAAM,WAAW,OAAO,MAAM,eAAe;AACvD,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAAA,cACpC;AACA,kBAAI,OAAO,MAAM,aAAa,cAAc;AAE3C,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAG,GAAG,EAAE,CAAC;AAAA,cACxD;AAAA,YACD;AAAA,UACD;AACA;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AAC3E,aAAO,MAAM,OAAO,aAAa,QAAQ,KAAK;AAAA,IAC/C,CAAC;AAED,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAES,SACR,qBAA8C,CAAC,GACf;AAChC,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACvE;AACD;", "names": []}
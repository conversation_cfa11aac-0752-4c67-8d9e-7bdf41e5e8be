{"version": 3, "sources": ["../../../src/prisma/pg/driver.ts"], "sourcesContent": ["import type { PrismaClient } from '@prisma/client/extension';\n\nimport { Prisma } from '@prisma/client';\n\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase, PgDialect } from '~/pg-core/index.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { PrismaPgQueryResultHKT } from './session.ts';\nimport { PrismaPgSession } from './session.ts';\n\nexport class PrismaPgDatabase extends PgDatabase<PrismaPgQueryResultHKT, Record<string, never>> {\n\tstatic override readonly [entityKind]: string = 'PrismaPgDatabase';\n\n\tconstructor(client: PrismaClient, logger: Logger | undefined) {\n\t\tconst dialect = new PgDialect();\n\t\tsuper(dialect, new PrismaPgSession(dialect, client, { logger }), undefined);\n\t}\n}\n\nexport type PrismaPgConfig = Omit<DrizzleConfig, 'schema'>;\n\nexport function drizzle(config: PrismaPgConfig = {}) {\n\tlet logger: Logger | undefined;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\treturn Prisma.defineExtension((client) => {\n\t\treturn client.$extends({\n\t\t\tname: 'drizzle',\n\t\t\tclient: {\n\t\t\t\t$drizzle: new PrismaPgDatabase(client, logger),\n\t\t\t},\n\t\t});\n\t});\n}\n"], "mappings": "AAEA,SAAS,cAAc;AAEvB,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,YAAY,iBAAiB;AAGtC,SAAS,uBAAuB;AAEzB,MAAM,yBAAyB,WAA0D;AAAA,EAC/F,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,QAAsB,QAA4B;AAC7D,UAAM,UAAU,IAAI,UAAU;AAC9B,UAAM,SAAS,IAAI,gBAAgB,SAAS,QAAQ,EAAE,OAAO,CAAC,GAAG,MAAS;AAAA,EAC3E;AACD;AAIO,SAAS,QAAQ,SAAyB,CAAC,GAAG;AACpD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,SAAO,OAAO,gBAAgB,CAAC,WAAW;AACzC,WAAO,OAAO,SAAS;AAAA,MACtB,MAAM;AAAA,MACN,QAAQ;AAAA,QACP,UAAU,IAAI,iBAAiB,QAAQ,MAAM;AAAA,MAC9C;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACF;", "names": []}
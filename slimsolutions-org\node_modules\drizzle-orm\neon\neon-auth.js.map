{"version": 3, "sources": ["../../src/neon/neon-auth.ts"], "sourcesContent": ["import { jsonb, pgSchema, text, timestamp } from '~/pg-core/index.ts';\n\nconst neonAuthSchema = pgSchema('neon_auth');\n\n/**\n * Table schema of the `users_sync` table used by <PERSON><PERSON> Auth.\n * This table automatically synchronizes and stores user data from external authentication providers.\n *\n * @schema neon_auth\n * @table users_sync\n */\nexport const usersSync = neonAuthSchema.table('users_sync', {\n\trawJson: jsonb('raw_json').notNull(),\n\tid: text().primaryKey().notNull(),\n\tname: text(),\n\temail: text(),\n\tcreatedAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),\n\tdeletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),\n\tupdatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),\n});\n"], "mappings": "AAAA,SAAS,OAAO,UAAU,MAAM,iBAAiB;AAEjD,MAAM,iBAAiB,SAAS,WAAW;AASpC,MAAM,YAAY,eAAe,MAAM,cAAc;AAAA,EAC3D,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,EACnC,IAAI,KAAK,EAAE,WAAW,EAAE,QAAQ;AAAA,EAChC,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AAAA,EACZ,WAAW,UAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAAA,EACzE,WAAW,UAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAAA,EACzE,WAAW,UAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1E,CAAC;", "names": []}
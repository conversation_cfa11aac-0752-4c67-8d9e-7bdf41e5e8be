{"version": 3, "sources": ["../../src/sqlite-core/alias.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\n\nimport type { SQLiteTable } from './table.ts';\nimport type { SQLiteViewBase } from './view-base.ts';\n\nexport function alias<TTable extends SQLiteTable | SQLiteViewBase, TAlias extends string>(\n\ttable: TTable,\n\talias: T<PERSON><PERSON><PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n"], "mappings": "AAAA,SAAS,8BAA8B;AAMhC,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,uBAAuBA,QAAO,KAAK,CAAC;AACjE;", "names": ["alias"]}
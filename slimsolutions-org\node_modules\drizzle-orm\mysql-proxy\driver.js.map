{"version": 3, "sources": ["../../src/mysql-proxy/driver.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { type MySqlRemotePreparedQueryHKT, type MySqlRemoteQueryResultHKT, MySqlRemoteSession } from './session.ts';\n\nexport class MySqlRemoteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends MySqlDatabase<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'MySqlRemoteDatabase';\n}\n\nexport type RemoteCallback = (\n\tsql: string,\n\tparams: any[],\n\tmethod: 'all' | 'execute',\n) => Promise<{ rows: any[]; insertId?: number; affectedRows?: number }>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tconfig: DrizzleConfig<TSchema> = {},\n): MySqlRemoteDatabase<TSchema> {\n\tconst dialect = new MySqlDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new MySqlRemoteSession(callback, dialect, schema, { logger });\n\treturn new MySqlRemoteDatabase(dialect, session, schema as any, 'default') as MySqlRemoteDatabase<TSchema>;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AAEP,SAA2E,0BAA0B;AAE9F,MAAM,4BAEH,cAA+E;AAAA,EACxF,QAA0B,UAAU,IAAY;AACjD;AAQO,SAAS,QACf,UACA,SAAiC,CAAC,GACH;AAC/B,QAAM,UAAU,IAAI,aAAa,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC1D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,mBAAmB,UAAU,SAAS,QAAQ,EAAE,OAAO,CAAC;AAC5E,SAAO,IAAI,oBAAoB,SAAS,SAAS,QAAe,SAAS;AAC1E;", "names": []}
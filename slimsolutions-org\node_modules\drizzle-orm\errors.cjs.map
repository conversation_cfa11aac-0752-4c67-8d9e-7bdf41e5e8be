{"version": 3, "sources": ["../src/errors.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport class DrizzleError extends Error {\n\tstatic readonly [entityKind]: string = 'DrizzleError';\n\n\tconstructor({ message, cause }: { message?: string; cause?: unknown }) {\n\t\tsuper(message);\n\t\tthis.name = 'DrizzleError';\n\t\tthis.cause = cause;\n\t}\n}\n\nexport class TransactionRollbackError extends DrizzleError {\n\tstatic override readonly [entityKind]: string = 'TransactionRollbackError';\n\n\tconstructor() {\n\t\tsuper({ message: 'Rollback' });\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAEpB,MAAM,qBAAqB,MAAM;AAAA,EACvC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,EAAE,SAAS,MAAM,GAA0C;AACtE,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACd;AACD;AAEO,MAAM,iCAAiC,aAAa;AAAA,EAC1D,QAA0B,wBAAU,IAAY;AAAA,EAEhD,cAAc;AACb,UAAM,EAAE,SAAS,WAAW,CAAC;AAAA,EAC9B;AACD;", "names": []}
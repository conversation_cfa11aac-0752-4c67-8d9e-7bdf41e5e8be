{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Heart, Target, Users, Star, TrendingUp, Shield, CheckCircle, ArrowRight, Play, Clock, Award, Zap } from \"lucide-react\";\nimport { useState } from \"react\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setMessage('');\n\n    try {\n      const response = await fetch('/api/newsletter', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          source: 'homepage_hero',\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setMessage('🎉 SUCCESS! Check your email for your FREE Fat-Burning Blueprint!');\n        setEmail('');\n      } else {\n        setMessage('❌ ' + (data.error || 'Something went wrong. Please try again.'));\n      }\n    } catch (error) {\n      setMessage('❌ Network error. Please check your connection and try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Urgent Banner */}\n      <div className=\"bg-red-600 text-white py-3 px-4 text-center font-bold animate-pulse\">\n        🔥 LIMITED TIME: Get 50% OFF Our Best-Selling Weight Loss Program - Only 24 Hours Left!\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"font-heading text-2xl font-bold text-primary\">\n              SlimSolutions\n            </div>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#features\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Features</a>\n              <a href=\"#testimonials\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Success Stories</a>\n              <Link href=\"/products\" className=\"text-primary font-semibold hover:text-primary/80 transition-colors\">Products</Link>\n              <a href=\"#contact\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Contact</a>\n            </div>\n            <Link href=\"/products\" className=\"bg-red-600 text-white px-6 py-2 rounded-lg font-ui font-bold hover:bg-red-700 transition-colors\">\n              GET ACCESS NOW\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-red-50 via-background to-yellow-50 py-16 px-4\">\n        <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            <div className=\"space-y-8\">\n              <div className=\"space-y-6\">\n                <div className=\"inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-bold animate-pulse\">\n                  <Award className=\"w-4 h-4 mr-2\" />\n                  #1 PROVEN Weight Loss System\n                </div>\n                <h1 className=\"font-heading text-4xl lg:text-6xl font-bold text-foreground leading-tight\">\n                  <span className=\"text-red-600\">LOSE 10-30 LBS</span>\n                  <span className=\"block\">In Just 30 Days</span>\n                  <span className=\"text-primary block text-2xl lg:text-4xl\">WITHOUT Starving Yourself!</span>\n                </h1>\n                <p className=\"text-xl text-muted-foreground leading-relaxed font-medium\">\n                  <strong className=\"text-red-600\">BREAKTHROUGH:</strong> Discover the secret \"Fat-Burning Switch\" that helped\n                  <span className=\"text-primary font-bold\"> 50,247 people</span> lose stubborn belly fat\n                  in just 30 days - even if you've tried everything before!\n                </p>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link href=\"/products\" className=\"bg-red-600 text-white px-8 py-4 rounded-lg font-ui font-bold text-lg hover:bg-red-700 transition-all transform hover:scale-105 flex items-center justify-center shadow-lg\">\n                  <Zap className=\"w-5 h-5 mr-2\" />\n                  GET INSTANT ACCESS NOW\n                </Link>\n                <button className=\"border-2 border-primary text-primary px-8 py-4 rounded-lg font-ui font-semibold text-lg hover:bg-primary hover:text-white transition-colors flex items-center justify-center\">\n                  <Play className=\"w-5 h-5 mr-2\" />\n                  Watch Transformation Video\n                </button>\n              </div>\n\n              {/* Urgency Timer */}\n              <div className=\"bg-yellow-100 border-l-4 border-yellow-500 p-4 rounded\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-5 h-5 text-yellow-600 mr-2\" />\n                  <p className=\"text-yellow-800 font-semibold\">\n                    ⚠️ HURRY! Special pricing expires in: <span className=\"text-red-600 font-bold\">23:47:32</span>\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-8 pt-4\">\n                <div className=\"text-center\">\n                  <div className=\"font-heading text-3xl font-bold text-green-600\">50,247</div>\n                  <div className=\"text-sm text-muted-foreground font-medium\">People Transformed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"font-heading text-3xl font-bold text-green-600\">97.3%</div>\n                  <div className=\"text-sm text-muted-foreground font-medium\">Success Rate</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"font-heading text-3xl font-bold text-green-600\">18.5 lbs</div>\n                  <div className=\"text-sm text-muted-foreground font-medium\">Average Loss</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative\">\n              <div className=\"bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border-4 border-yellow-400 relative\">\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-6 py-2 rounded-full font-bold text-sm\">\n                  🎁 FREE GIFT INSIDE\n                </div>\n\n                <div className=\"text-center mb-6 mt-4\">\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    Get Your FREE Fat-Burning Blueprint\n                  </h3>\n                  <p className=\"text-gray-600 font-medium\">\n                    The exact system that helped Sarah lose 32 lbs in 30 days!\n                  </p>\n                </div>\n\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\n                  <div>\n                    <input\n                      type=\"email\"\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      placeholder=\"Enter your email for INSTANT access\"\n                      className=\"w-full px-4 py-4 rounded-lg border-2 border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary text-gray-900 placeholder-gray-500 font-medium\"\n                      required\n                    />\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className=\"w-full bg-green-600 text-white px-6 py-4 rounded-lg font-bold text-lg hover:bg-green-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg\"\n                  >\n                    {isSubmitting ? 'SENDING YOUR BLUEPRINT...' : '🎁 SEND ME MY FREE BLUEPRINT NOW!'}\n                  </button>\n                </form>\n\n                {message && (\n                  <div className={`mt-4 p-4 rounded-lg text-center font-medium ${\n                    message.includes('🎉')\n                      ? 'bg-green-100 text-green-800 border-2 border-green-200'\n                      : 'bg-red-100 text-red-800 border-2 border-red-200'\n                  }`}>\n                    {message}\n                  </div>\n                )}\n\n                <div className=\"mt-4 space-y-2\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600 mr-2\" />\n                    Instant download - no waiting\n                  </div>\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600 mr-2\" />\n                    100% free - no credit card required\n                  </div>\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600 mr-2\" />\n                    Join 50,000+ successful members\n                  </div>\n                </div>\n\n                <p className=\"text-xs text-gray-500 text-center mt-4\">\n                  🔒 Your email is 100% secure. No spam, ever.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Problem/Solution Section */}\n      <section className=\"py-16 px-4 bg-gray-50\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"font-heading text-4xl font-bold text-gray-900 mb-6\">\n              Are You TIRED of Trying Diet After Diet...\n              <span className=\"text-red-600 block\">Only to Gain the Weight Back?</span>\n            </h2>\n            <p className=\"text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed\">\n              If you've tried \"everything\" but still struggle with stubborn belly fat, it's NOT your fault.\n              The problem is you've been following outdated advice that actually <strong>slows down</strong> your metabolism!\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <div className=\"space-y-6\">\n              <h3 className=\"text-2xl font-bold text-gray-900\">Here's What's REALLY Keeping You Overweight:</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <span className=\"text-red-600 font-bold text-sm\">✗</span>\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Starvation diets</strong> that destroy your metabolism</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <span className=\"text-red-600 font-bold text-sm\">✗</span>\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Extreme workouts</strong> that burn you out in weeks</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <span className=\"text-red-600 font-bold text-sm\">✗</span>\n                  </div>\n                  <p className=\"text-gray-700\"><strong>\"One-size-fits-all\"</strong> programs that ignore your body type</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <span className=\"text-red-600 font-bold text-sm\">✗</span>\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Complicated meal plans</strong> that are impossible to follow</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-6\">\n              <h3 className=\"text-2xl font-bold text-green-700\">Our Revolutionary Approach:</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Metabolic Reset Protocol</strong> that turns your body into a fat-burning machine</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <p className=\"text-gray-700\"><strong>15-minute workouts</strong> that fit into any schedule</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Personalized meal plans</strong> based on your food preferences</p>\n                </div>\n                <div className=\"flex items-start\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <p className=\"text-gray-700\"><strong>Simple 3-step system</strong> anyone can follow</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link href=\"/products\" className=\"bg-red-600 text-white px-10 py-4 rounded-lg font-bold text-xl hover:bg-red-700 transition-all transform hover:scale-105 shadow-lg inline-flex items-center\">\n              <Zap className=\"w-6 h-6 mr-2\" />\n              YES! I Want This System Now\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Social Proof Section */}\n      <section id=\"testimonials\" className=\"py-16 px-4 bg-white\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"font-heading text-4xl font-bold text-gray-900 mb-6\">\n              <span className=\"text-red-600\">WARNING:</span> These Results Are NOT Typical...\n              <span className=\"block text-2xl text-gray-700 mt-2\">They're BETTER Than Average!</span>\n            </h2>\n            <p className=\"text-xl text-gray-700 max-w-4xl mx-auto\">\n              See what happens when you finally use a system that actually works with your body instead of against it\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border-2 border-green-200 relative\">\n              <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-4 py-1 rounded-full text-sm font-bold\">\n                VERIFIED RESULT\n              </div>\n              <div className=\"flex items-center mb-4 mt-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-800 mb-6 font-medium text-lg\">\n                \"I lost 32 pounds in just 30 days! I couldn't believe it when I stepped on the scale.\n                This is the ONLY thing that ever worked for me.\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-16 h-16 bg-green-200 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"font-bold text-green-800 text-lg\">SM</span>\n                </div>\n                <div>\n                  <div className=\"font-bold text-gray-900 text-lg\">Sarah M.</div>\n                  <div className=\"text-green-700 font-semibold\">Lost 32 lbs in 30 days</div>\n                  <div className=\"text-sm text-gray-600\">Age 42, Mother of 3</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-blue-200 relative\">\n              <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-bold\">\n                VERIFIED RESULT\n              </div>\n              <div className=\"flex items-center mb-4 mt-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-800 mb-6 font-medium text-lg\">\n                \"Down 47 pounds and my doctor says my blood pressure is perfect now.\n                My wife says I look 10 years younger!\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-16 h-16 bg-blue-200 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"font-bold text-blue-800 text-lg\">MJ</span>\n                </div>\n                <div>\n                  <div className=\"font-bold text-gray-900 text-lg\">Mike J.</div>\n                  <div className=\"text-blue-700 font-semibold\">Lost 47 lbs in 8 weeks</div>\n                  <div className=\"text-sm text-gray-600\">Age 55, Executive</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 border-2 border-purple-200 relative\">\n              <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-bold\">\n                VERIFIED RESULT\n              </div>\n              <div className=\"flex items-center mb-4 mt-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-800 mb-6 font-medium text-lg\">\n                \"I've tried everything for 20 years. This is the first time I lost weight\n                AND kept it off. It's been 6 months and I'm still at my goal weight!\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"font-bold text-purple-800 text-lg\">LK</span>\n                </div>\n                <div>\n                  <div className=\"font-bold text-gray-900 text-lg\">Lisa K.</div>\n                  <div className=\"text-purple-700 font-semibold\">Lost 28 lbs, kept it off 6 months</div>\n                  <div className=\"text-sm text-gray-600\">Age 38, Teacher</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <div className=\"bg-yellow-100 border-2 border-yellow-400 rounded-lg p-6 max-w-2xl mx-auto\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                🚨 ATTENTION: Results Like These Are Only Possible With Our Complete System\n              </h3>\n              <p className=\"text-gray-700 font-medium\">\n                Don't waste another day with methods that don't work. Get the EXACT system these people used.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Products Section */}\n      <section id=\"products\" className=\"py-16 px-4 bg-gradient-to-br from-red-600 to-red-700 text-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"font-heading text-4xl font-bold mb-4\">\n              🔥 Get The Complete SlimSolutions System\n            </h2>\n            <p className=\"text-xl text-red-100 max-w-3xl mx-auto\">\n              Everything you need to lose 10-30 lbs in the next 30 days - guaranteed or your money back!\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n            <div className=\"space-y-6\">\n              <h3 className=\"text-3xl font-bold\">What You Get Today:</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <CheckCircle className=\"w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-semibold text-lg\">The Metabolic Reset Protocol</p>\n                    <p className=\"text-red-100\">Turn your body into a 24/7 fat-burning machine (Value: $197)</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircle className=\"w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-semibold text-lg\">Personalized Meal Plans</p>\n                    <p className=\"text-red-100\">Custom plans based on your food preferences (Value: $147)</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircle className=\"w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-semibold text-lg\">15-Minute Fat-Burning Workouts</p>\n                    <p className=\"text-red-100\">No gym required, works for any fitness level (Value: $97)</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircle className=\"w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-semibold text-lg\">24/7 Support Community</p>\n                    <p className=\"text-red-100\">Private Facebook group with 50,000+ members (Value: $97)</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircle className=\"w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-semibold text-lg\">Progress Tracking App</p>\n                    <p className=\"text-red-100\">Track your results and stay motivated (Value: $47)</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white text-gray-900 rounded-2xl p-8 shadow-2xl relative\">\n              <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-6 py-2 rounded-full font-bold\">\n                LIMITED TIME OFFER\n              </div>\n\n              <div className=\"text-center mt-4\">\n                <div className=\"text-gray-500 line-through text-xl mb-2\">Regular Price: $585</div>\n                <div className=\"text-5xl font-bold text-red-600 mb-2\">$97</div>\n                <div className=\"text-green-600 font-bold text-lg mb-6\">Save $488 Today!</div>\n\n                <Link href=\"/products\" className=\"block w-full bg-green-600 text-white px-8 py-4 rounded-lg font-bold text-xl hover:bg-green-700 transition-all transform hover:scale-105 shadow-lg mb-4\">\n                  🎁 GET INSTANT ACCESS NOW\n                </Link>\n\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"flex items-center justify-center\">\n                    <Shield className=\"w-4 h-4 mr-2\" />\n                    60-Day Money-Back Guarantee\n                  </div>\n                  <div className=\"flex items-center justify-center\">\n                    <Clock className=\"w-4 h-4 mr-2\" />\n                    Instant Digital Access\n                  </div>\n                  <div className=\"flex items-center justify-center\">\n                    <CheckCircle className=\"w-4 h-4 mr-2\" />\n                    No Monthly Fees\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Final Urgent CTA */}\n      <section className=\"py-16 px-4 bg-black text-white\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-4xl font-bold mb-6 text-yellow-400\">\n            ⚠️ LAST CHANCE WARNING ⚠️\n          </h2>\n          <p className=\"text-2xl mb-4\">\n            Don't Let Another Year Go By Feeling Unhappy With Your Body\n          </p>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            This special 83% discount expires at midnight tonight. After that, the price goes back to $585.\n            Don't miss your chance to get the same system that helped 50,247 people lose weight.\n          </p>\n\n          <div className=\"bg-red-600 text-white p-6 rounded-lg mb-8 max-w-2xl mx-auto\">\n            <p className=\"text-lg font-bold mb-2\">🔥 ONLY 47 SPOTS LEFT AT THIS PRICE 🔥</p>\n            <p className=\"text-yellow-300\">Timer: 23:47:32 remaining</p>\n          </div>\n\n          <Link href=\"/products\" className=\"bg-yellow-400 text-black px-12 py-6 rounded-lg font-bold text-2xl hover:bg-yellow-300 transition-all transform hover:scale-105 shadow-lg inline-flex items-center\">\n            <Zap className=\"w-8 h-8 mr-3\" />\n            SECURE MY TRANSFORMATION NOW\n          </Link>\n\n          <p className=\"text-gray-400 text-sm mt-6\">\n            60-Day Money-Back Guarantee • Instant Access • One-Time Payment\n          </p>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid md:grid-cols-3 gap-8 mb-8\">\n            <div>\n              <h3 className=\"font-heading text-2xl font-bold mb-4\">SlimSolutions</h3>\n              <p className=\"text-gray-400 mb-6\">\n                Transforming lives through proven weight loss solutions since 2020.\n                Join the 50,000+ success stories.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"font-heading text-lg font-semibold mb-4\">Quick Links</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/#testimonials\" className=\"text-gray-400 hover:text-white transition-colors\">Success Stories</Link></li>\n                <li><Link href=\"/products\" className=\"text-gray-400 hover:text-white transition-colors\">Products</Link></li>\n                <li><Link href=\"/#features\" className=\"text-gray-400 hover:text-white transition-colors\">How It Works</Link></li>\n                <li><Link href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">Contact</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"font-heading text-lg font-semibold mb-4\">Legal</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors\">Privacy Policy</Link></li>\n                <li><Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors\">Terms of Service</Link></li>\n                <li><Link href=\"/disclaimer\" className=\"text-gray-400 hover:text-white transition-colors\">Disclaimer</Link></li>\n                <li><Link href=\"/refund\" className=\"text-gray-400 hover:text-white transition-colors\">Refund Policy</Link></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-800 pt-8 text-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 SlimSolutions. All rights reserved. |\n              <span className=\"text-xs\"> Results may vary. Individual results are not guaranteed. Consult your doctor before starting any weight loss program.</span>\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,SAAS;YACX,OAAO;gBACL,WAAW,OAAO,CAAC,KAAK,KAAK,IAAI,yCAAyC;YAC5E;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BAAsE;;;;;;0BAKrF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA+C;;;;;;0CAG9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAgE;;;;;;kDAC9F,6LAAC;wCAAE,MAAK;wCAAgB,WAAU;kDAAgE;;;;;;kDAClG,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAqE;;;;;;kDACtG,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAgE;;;;;;;;;;;;0CAE/F,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAkG;;;;;;;;;;;;;;;;;;;;;;0BAQzI,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGpC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAAe;;;;;;sEAC/B,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;sEACxB,6LAAC;4DAAK,WAAU;sEAA0C;;;;;;;;;;;;8DAE5D,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAO,WAAU;sEAAe;;;;;;wDAAsB;sEACvD,6LAAC;4DAAK,WAAU;sEAAyB;;;;;;wDAAqB;;;;;;;;;;;;;sDAKlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;;sEAC/B,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAMrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAE,WAAU;;4DAAgC;0EACL,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;sDAKrF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiD;;;;;;sEAChE,6LAAC;4DAAI,WAAU;sEAA4C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiD;;;;;;sEAChE,6LAAC;4DAAI,WAAU;sEAA4C;;;;;;;;;;;;8DAE7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiD;;;;;;sEAChE,6LAAC;4DAAI,WAAU;sEAA4C;;;;;;;;;;;;;;;;;;;;;;;;8CAKjE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqH;;;;;;0DAIpI,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAGtD,6LAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;0DAK3C,6LAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,6LAAC;kEACC,cAAA,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4DACxC,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;kEAIZ,6LAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,eAAe,8BAA8B;;;;;;;;;;;;4CAIjD,yBACC,6LAAC;gDAAI,WAAW,AAAC,+CAIhB,OAHC,QAAQ,QAAQ,CAAC,QACb,0DACA;0DAEH;;;;;;0DAIL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;kEAGzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;kEAGzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;;;;;;;0DAK3D,6LAAC;gDAAE,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhE,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqD;sDAEjE,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAE,WAAU;;wCAA0D;sDAEF,6LAAC;sDAAO;;;;;;wCAAmB;;;;;;;;;;;;;sCAIlG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAiC;;;;;;;;;;;sEAEnD,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAAyB;;;;;;;;;;;;;8DAEhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAiC;;;;;;;;;;;sEAEnD,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAAyB;;;;;;;;;;;;;8DAEhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAiC;;;;;;;;;;;sEAEnD,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAA4B;;;;;;;;;;;;;8DAEnE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAiC;;;;;;;;;;;sEAEnD,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;8CAK1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAAiC;;;;;;;;;;;;;8DAExE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAA2B;;;;;;;;;;;;;8DAElE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAAgC;;;;;;;;;;;;;8DAEvE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAE,WAAU;;8EAAgB,6LAAC;8EAAO;;;;;;gEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM1E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;;kDAC/B,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,6LAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAe;sDAC9C,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuH;;;;;;sDAGtI,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAItD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;8DAErD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAA+B;;;;;;sEAC9C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsH;;;;;;sDAGrI,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAItD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAkC;;;;;;;;;;;8DAEpD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAA8B;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAwH;;;;;;sDAGvI,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAItD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;8DAEtD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;sCAKxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgH;;;;;;sDAI/H,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DAEvD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAyJ;;;;;;8DAI1L,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGrC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;8CACtC,6LAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;sCAGjC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;;8CAC/B,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIlC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAMpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA0C;;;;;;sDACxD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,WAAU;kEAAmD;;;;;;;;;;;8DAC7F,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmD;;;;;;;;;;;8DACxF,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAAmD;;;;;;;;;;;8DACzF,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;8CAI3F,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA0C;;;;;;sDACxD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAmD;;;;;;;;;;;8DACvF,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAmD;;;;;;;;;;;8DACrF,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAmD;;;;;;;;;;;8DAC1F,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK5F,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;kDAEnC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GA9hBwB;KAAA", "debugId": null}}]}
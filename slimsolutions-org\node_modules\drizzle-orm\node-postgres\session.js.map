{"version": 3, "sources": ["../../src/node-postgres/session.ts"], "sourcesContent": ["import type { Client, PoolClient, QueryArrayConfig, QueryConfig, QueryResult, QueryResultRow } from 'pg';\nimport pg from 'pg';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nconst { Pool, types } = pg;\n\nexport type NodePgClient = pg.Pool | PoolClient | Client;\n\nexport class NodePgPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'NodePgPreparedQuery';\n\n\tprivate rawQueryConfig: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: NodePgClient,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t\tthis.rawQueryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// numeric[]\n\t\t\t\t\tif (typeId === 1231) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp[]\n\t\t\t\t\tif (typeId === 1115) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp with timezone[]\n\t\t\t\t\tif (typeId === 1185) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// interval[]\n\t\t\t\t\tif (typeId === 1187) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// date[]\n\t\t\t\t\tif (typeId === 1182) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// numeric[]\n\t\t\t\t\tif (typeId === 1231) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp[]\n\t\t\t\t\tif (typeId === 1115) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp with timezone[]\n\t\t\t\t\tif (typeId === 1185) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// interval[]\n\t\t\t\t\tif (typeId === 1187) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// date[]\n\t\t\t\t\tif (typeId === 1182) {\n\t\t\t\t\t\treturn (val) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\n\t\t\tconst { fields, rawQueryConfig: rawQuery, client, queryConfig: query, joinsNotNullableMap, customResultMapper } =\n\t\t\t\tthis;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async (span) => {\n\t\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t\t'drizzle.query.name': rawQuery.name,\n\t\t\t\t\t\t'drizzle.query.text': rawQuery.text,\n\t\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t\t});\n\t\t\t\t\treturn this.queryWithCache(rawQuery.text, params, async () => {\n\t\t\t\t\t\treturn await client.query(rawQuery, params);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst result = await tracer.startActiveSpan('drizzle.driver.execute', (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.name': query.name,\n\t\t\t\t\t'drizzle.query.text': query.text,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn this.queryWithCache(query.text, params, async () => {\n\t\t\t\t\treturn await client.query(query, params);\n\t\t\t\t});\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(result.rows)\n\t\t\t\t\t: result.rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.name': this.rawQueryConfig.name,\n\t\t\t\t\t'drizzle.query.text': this.rawQueryConfig.text,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn this.queryWithCache(this.rawQueryConfig.text, params, async () => {\n\t\t\t\t\treturn this.client.query(this.rawQueryConfig, params);\n\t\t\t\t}).then((result) => result.rows);\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface NodePgSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class NodePgSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NodePgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NodePgSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: NodePgClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NodePgSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T> {\n\t\treturn new NodePgPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: NodePgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst session = this.client instanceof Pool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new NodePgSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new NodePgTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin${config ? sql` ${tx.getTransactionConfigSQL(config)}` : undefined}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof Pool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as PoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<{ rows: [{ count: string }] }>(sql);\n\t\treturn Number(\n\t\t\tres['rows'][0]['count'],\n\t\t);\n\t}\n}\n\nexport class NodePgTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NodePgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NodePgTransaction';\n\n\toverride async transaction<T>(transaction: (tx: NodePgTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new NodePgTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface NodePgQueryResultHKT extends PgQueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "mappings": "AACA,OAAO,QAAQ;AACf,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAC3B,SAAsB,kBAAkB;AAExC,SAAS,qBAAqB;AAG9B,SAAS,iBAAiB,iBAAiB;AAE3C,SAAS,kBAAwC,WAAW;AAC5D,SAAS,cAAc;AACvB,SAAsB,oBAAoB;AAE1C,MAAM,EAAE,MAAM,MAAM,IAAI;AAIjB,MAAM,4BAA2D,gBAAmB;AAAA,EAM1F,YACS,QACA,aACA,QACA,QACR,OACA,eAIA,aACQ,QACR,MACQ,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAf7D;AACA;AACA;AACA;AAOA;AAEA;AACA;AAGR,SAAK,iBAAiB;AAAA,MACrB;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,MAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,iBAAO,MAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AACA,SAAK,cAAc;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,MAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AACA,cAAI,WAAW,MAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,MAAM;AACpB,mBAAO,CAAC,QAAQ;AAAA,UACjB;AAEA,iBAAO,MAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EA7GA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EA4GR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,OAAO,gBAAgB,mBAAmB,YAAY;AAC5D,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,WAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AAErD,YAAM,EAAE,QAAQ,gBAAgB,UAAU,QAAQ,aAAa,OAAO,qBAAqB,mBAAmB,IAC7G;AACD,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,OAAO,gBAAgB,0BAA0B,OAAO,SAAS;AACvE,gBAAM,cAAc;AAAA,YACnB,sBAAsB,SAAS;AAAA,YAC/B,sBAAsB,SAAS;AAAA,YAC/B,wBAAwB,KAAK,UAAU,MAAM;AAAA,UAC9C,CAAC;AACD,iBAAO,KAAK,eAAe,SAAS,MAAM,QAAQ,YAAY;AAC7D,mBAAO,MAAM,OAAO,MAAM,UAAU,MAAM;AAAA,UAC3C,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAEA,YAAM,SAAS,MAAM,OAAO,gBAAgB,0BAA0B,CAAC,SAAS;AAC/E,cAAM,cAAc;AAAA,UACnB,sBAAsB,MAAM;AAAA,UAC5B,sBAAsB,MAAM;AAAA,UAC5B,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AACD,eAAO,KAAK,eAAe,MAAM,MAAM,QAAQ,YAAY;AAC1D,iBAAO,MAAM,OAAO,MAAM,OAAO,MAAM;AAAA,QACxC,CAAC;AAAA,MACF,CAAC;AAED,aAAO,OAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,OAAO,IAAI,IAC9B,OAAO,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MAC1F,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,WAAO,OAAO,gBAAgB,mBAAmB,MAAM;AACtD,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,WAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AACrD,aAAO,OAAO,gBAAgB,0BAA0B,CAAC,SAAS;AACjE,cAAM,cAAc;AAAA,UACnB,sBAAsB,KAAK,eAAe;AAAA,UAC1C,sBAAsB,KAAK,eAAe;AAAA,UAC1C,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AACD,eAAO,KAAK,eAAe,KAAK,eAAe,MAAM,QAAQ,YAAY;AACxE,iBAAO,KAAK,OAAO,MAAM,KAAK,gBAAgB,MAAM;AAAA,QACrD,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,MAChC,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,sBAGH,UAAsD;AAAA,EAM/D,YACS,QACR,SACQ,QACA,UAAgC,CAAC,GACxC;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,UAAU,KAAK,kBAAkB,OACpC,IAAI,cAAc,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IACtF;AACH,UAAM,KAAK,IAAI,kBAAwC,KAAK,SAAS,SAAS,KAAK,MAAM;AACzF,UAAM,GAAG,QAAQ,WAAW,SAAS,OAAO,GAAG,wBAAwB,MAAM,CAAC,KAAK,MAAS,EAAE;AAC9F,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,WAAW;AAC5B,aAAO;AAAA,IACR,SAAS,OAAO;AACf,YAAM,GAAG,QAAQ,aAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,KAAK,kBAAkB,MAAM;AAChC,QAAC,QAAQ,OAAsB,QAAQ;AAAA,MACxC;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,MAAMA,MAA2B;AAC/C,UAAM,MAAM,MAAM,KAAK,QAAuCA,IAAG;AACjE,WAAO;AAAA,MACN,IAAI,MAAM,EAAE,CAAC,EAAE,OAAO;AAAA,IACvB;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,cAA0D;AAAA,EACnE,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,aAAsF;AACnH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["sql"]}
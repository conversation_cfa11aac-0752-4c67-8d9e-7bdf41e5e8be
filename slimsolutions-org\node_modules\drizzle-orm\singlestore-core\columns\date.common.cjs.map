{"version": 3, "sources": ["../../../src/singlestore-core/columns/date.common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnDataType,\n\tHasDefault,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport interface SingleStoreDateColumnBaseConfig {\n\thasOnUpdateNow: boolean;\n}\n\nexport abstract class SingleStoreDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends SingleStoreColumnBuilder<T, TRuntimeConfig & SingleStoreDateColumnBaseConfig, TExtraConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateColumnBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n\n\tonUpdateNow(): HasDefault<this> {\n\t\tthis.config.hasOnUpdateNow = true;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n}\n\nexport abstract class SingleStoreDateBaseColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SingleStoreColumn<T, SingleStoreDateColumnBaseConfig & TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateColumn';\n\n\treadonly hasOnUpdateNow: boolean = this.config.hasOnUpdateNow;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,oBAA2B;AAC3B,iBAAoB;AACpB,oBAA4D;AAMrD,MAAe,yCAIZ,uCAA4F;AAAA,EACrG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAa;AACZ,WAAO,KAAK,QAAQ,qBAAU;AAAA,EAC/B;AAAA,EAEA,cAAgC;AAC/B,SAAK,OAAO,iBAAiB;AAC7B,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAe,kCAGZ,gCAAuE;AAAA,EAChF,QAA0B,wBAAU,IAAY;AAAA,EAEvC,iBAA0B,KAAK,OAAO;AAChD;", "names": []}
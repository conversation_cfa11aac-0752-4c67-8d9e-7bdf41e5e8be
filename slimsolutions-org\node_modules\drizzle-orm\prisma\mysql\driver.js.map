{"version": 3, "sources": ["../../../src/prisma/mysql/driver.ts"], "sourcesContent": ["import type { PrismaClient } from '@prisma/client/extension';\n\nimport { Prisma } from '@prisma/client';\n\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase, MySqlDialect } from '~/mysql-core/index.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { PrismaMySqlPreparedQueryHKT, PrismaMySqlQueryResultHKT } from './session.ts';\nimport { PrismaMySqlSession } from './session.ts';\n\nexport class PrismaMySqlDatabase\n\textends MySqlDatabase<PrismaMySqlQueryResultHKT, PrismaMySqlPreparedQueryHKT, Record<string, never>>\n{\n\tstatic override readonly [entityKind]: string = 'PrismaMySqlDatabase';\n\n\tconstructor(client: PrismaClient, logger: Logger | undefined) {\n\t\tconst dialect = new MySqlDialect();\n\t\tsuper(dialect, new PrismaMySqlSession(dialect, client, { logger }), undefined, 'default');\n\t}\n}\n\nexport type PrismaMySqlConfig = Omit<DrizzleConfig, 'schema'>;\n\nexport function drizzle(config: PrismaMySqlConfig = {}) {\n\tlet logger: Logger | undefined;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\treturn Prisma.defineExtension((client) => {\n\t\treturn client.$extends({\n\t\t\tname: 'drizzle',\n\t\t\tclient: {\n\t\t\t\t$drizzle: new PrismaMySqlDatabase(client, logger),\n\t\t\t},\n\t\t});\n\t});\n}\n"], "mappings": "AAEA,SAAS,cAAc;AAEvB,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,eAAe,oBAAoB;AAG5C,SAAS,0BAA0B;AAE5B,MAAM,4BACJ,cACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,QAAsB,QAA4B;AAC7D,UAAM,UAAU,IAAI,aAAa;AACjC,UAAM,SAAS,IAAI,mBAAmB,SAAS,QAAQ,EAAE,OAAO,CAAC,GAAG,QAAW,SAAS;AAAA,EACzF;AACD;AAIO,SAAS,QAAQ,SAA4B,CAAC,GAAG;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,SAAO,OAAO,gBAAgB,CAAC,WAAW;AACzC,WAAO,OAAO,SAAS;AAAA,MACtB,MAAM;AAAA,MACN,QAAQ;AAAA,QACP,UAAU,IAAI,oBAAoB,QAAQ,MAAM;AAAA,MACjD;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACF;", "names": []}
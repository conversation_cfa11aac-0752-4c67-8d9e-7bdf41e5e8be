{"version": 3, "sources": ["../../../src/pg-core/columns/inet.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgInetBuilderInitial<TName extends string> = PgInetBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgInet';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgInetBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgInet'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgInetBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgInet');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInet<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInet<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInet<T extends ColumnBaseConfig<'string', 'PgInet'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgInet';\n\n\tgetSQLType(): string {\n\t\treturn 'inet';\n\t}\n}\n\nexport function inet(): PgInetBuilderInitial<''>;\nexport function inet<TName extends string>(name: TName): PgInetBuilderInitial<TName>;\nexport function inet(name?: string) {\n\treturn new PgInetBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAWnC,MAAM,sBAA6E,8BAAmB;AAAA,EAC5G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eAA+D,uBAAY;AAAA,EACvF,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,cAAc,QAAQ,EAAE;AACpC;", "names": []}
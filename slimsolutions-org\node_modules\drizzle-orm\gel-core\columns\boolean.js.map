{"version": 3, "sources": ["../../../src/gel-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelBooleanBuilderInitial<TName extends string> = GelBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'GelBoolean';\n\tdata: boolean;\n\tdriverParam: boolean;\n\tenumValues: undefined;\n}>;\n\nexport class GelBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'GelBoolean'>> extends GelColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'GelBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'GelBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelBoolean<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelBoolean<T extends ColumnBaseConfig<'boolean', 'GelBoolean'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n}\n\nexport function boolean(): GelBooleanBuilderInitial<''>;\nexport function boolean<TName extends string>(name: TName): GelBooleanBuilderInitial<TName>;\nexport function boolean(name?: string) {\n\treturn new GelBooleanBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,0BAAsF,iBAAoB;AAAA,EACtH,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,WAAW,YAAY;AAAA,EACpC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAwE,UAAa;AAAA,EACjG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC;", "names": []}
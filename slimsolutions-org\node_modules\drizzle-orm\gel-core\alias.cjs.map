{"version": 3, "sources": ["../../src/gel-core/alias.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\n\nimport type { GelTable } from './table.ts';\nimport type { GelViewBase } from './view-base.ts';\n\nexport function alias<TTable extends GelTable | GelViewBase, TAlias extends string>(\n\ttable: TTable,\n\talias: T<PERSON><PERSON><PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAuC;AAMhC,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,oCAAuBA,QAAO,KAAK,CAAC;AACjE;", "names": ["alias"]}
{"version": 3, "sources": ["../../../src/singlestore-core/query-builders/delete.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport type {\n\tAnySingleStoreQueryResultHKT,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n\tSingleStorePreparedQueryConfig,\n\tSingleStoreQueryResultHKT,\n\tSingleStoreQueryResultKind,\n\tSingleStoreSession,\n} from '~/singlestore-core/session.ts';\nimport type { SingleStoreTable } from '~/singlestore-core/table.ts';\nimport type { Placeholder, Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport type { ValueOrArray } from '~/utils.ts';\nimport type { SingleStoreColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type { SelectedFieldsOrdered } from './select.types.ts';\n\nexport type SingleStoreDeleteWithout<\n\tT extends AnySingleStoreDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tSingleStoreDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['preparedQueryHKT'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type SingleStoreDelete<\n\tTTable extends SingleStoreTable = SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT = AnySingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n> = SingleStoreDeleteBase<TTable, TQueryResult, TPreparedQueryHKT, true, never>;\n\nexport interface SingleStoreDeleteConfig {\n\twhere?: SQL | undefined;\n\tlimit?: number | Placeholder;\n\torderBy?: (SingleStoreColumn | SQL | SQL.Aliased)[];\n\ttable: SingleStoreTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type SingleStoreDeletePrepare<T extends AnySingleStoreDeleteBase> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tSingleStorePreparedQueryConfig & {\n\t\texecute: SingleStoreQueryResultKind<T['_']['queryResult'], never>;\n\t\titerator: never;\n\t},\n\ttrue\n>;\n\ntype SingleStoreDeleteDynamic<T extends AnySingleStoreDeleteBase> = SingleStoreDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT']\n>;\n\ntype AnySingleStoreDeleteBase = SingleStoreDeleteBase<any, any, any, any, any>;\n\nexport interface SingleStoreDeleteBase<\n\tTTable extends SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<SingleStoreQueryResultKind<TQueryResult, never>> {\n\treadonly _: {\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t};\n}\n\nexport class SingleStoreDeleteBase<\n\tTTable extends SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<SingleStoreQueryResultKind<TQueryResult, never>> implements SQLWrapper {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDelete';\n\n\tprivate config: SingleStoreDeleteConfig;\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: SingleStoreSession,\n\t\tprivate dialect: SingleStoreDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): SingleStoreDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\torderBy(\n\t\tbuilder: (deleteTable: TTable) => ValueOrArray<SingleStoreColumn | SQL | SQL.Aliased>,\n\t): SingleStoreDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (SingleStoreColumn | SQL | SQL.Aliased)[]): SingleStoreDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(deleteTable: TTable) => ValueOrArray<SingleStoreColumn | SQL | SQL.Aliased>]\n\t\t\t| (SingleStoreColumn | SQL | SQL.Aliased)[]\n\t): SingleStoreDeleteWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as any,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t} else {\n\t\t\tconst orderByArray = columns as (SingleStoreColumn | SQL | SQL.Aliased)[];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\tlimit(limit: number | Placeholder): SingleStoreDeleteWithout<this, TDynamic, 'limit'> {\n\t\tthis.config.limit = limit;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tprepare(): SingleStoreDeletePrepare<this> {\n\t\treturn this.session.prepareQuery(\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\ttype: 'delete',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t},\n\t\t) as SingleStoreDeletePrepare<this>;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this.prepare().execute(placeholderValues);\n\t};\n\n\tprivate createIterator = (): ReturnType<this['prepare']>['iterator'] => {\n\t\tconst self = this;\n\t\treturn async function*(placeholderValues) {\n\t\t\tyield* self.prepare().iterator(placeholderValues);\n\t\t};\n\t};\n\n\titerator = this.createIterator();\n\n\t$dynamic(): SingleStoreDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,2BAA6B;AAC7B,6BAAsC;AActC,mBAAsB;AAGtB,mBAAiC;AAmE1B,MAAM,8BAQH,kCAAoF;AAAA,EAK7F,YACS,OACA,SACA,SACR,UACC;AACD,UAAM;AALE;AACA;AACA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;AAAA,EACjC;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyCR,MAAM,OAA2E;AAChF,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAMA,WACI,SAGmD;AACtD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO;AAAA,UACtC,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAChE,WAAK,OAAO,UAAU;AAAA,IACvB,OAAO;AACN,YAAM,eAAe;AACrB,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,OAAgF;AACrF,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,UAA0C;AACzC,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,YAAQ,+BAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+C;AACvE,UAAM,OAAO;AACb,WAAO,iBAAgB,mBAAmB;AACzC,aAAO,KAAK,QAAQ,EAAE,SAAS,iBAAiB;AAAA,IACjD;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,eAAe;AAAA,EAE/B,WAA2C;AAC1C,WAAO;AAAA,EACR;AACD;", "names": []}
{"version": 3, "sources": ["../../../src/singlestore-core/columns/binary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreBinaryBuilderInitial<TName extends string> = SingleStoreBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreBinary'>>\n\textends SingleStoreColumnBuilder<\n\t\tT,\n\t\tSingleStoreBinaryConfig\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBinaryBuilder';\n\n\tconstructor(name: T['name'], length: number | undefined) {\n\t\tsuper(name, 'string', 'SingleStoreBinary');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreBinary<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreBinary<T extends ColumnBaseConfig<'string', 'SingleStoreBinary'>> extends SingleStoreColumn<\n\tT,\n\tSingleStoreBinaryConfig\n> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\toverride mapFromDriverValue(value: string | Buffer | Uint8Array): string {\n\t\tif (typeof value === 'string') return value;\n\t\tif (Buffer.isBuffer(value)) return value.toString();\n\n\t\tconst str: string[] = [];\n\t\tfor (const v of value) {\n\t\t\tstr.push(v === 49 ? '1' : '0');\n\t\t}\n\n\t\treturn str.join('');\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `binary` : `binary(${this.length})`;\n\t}\n}\n\nexport interface SingleStoreBinaryConfig {\n\tlength?: number;\n}\n\nexport function binary(): SingleStoreBinaryBuilderInitial<''>;\nexport function binary(\n\tconfig?: SingleStoreBinaryConfig,\n): SingleStoreBinaryBuilderInitial<''>;\nexport function binary<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreBinaryConfig,\n): SingleStoreBinaryBuilderInitial<TName>;\nexport function binary(a?: string | SingleStoreBinaryConfig, b: SingleStoreBinaryConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreBinaryConfig>(a, b);\n\treturn new SingleStoreBinaryBuilder(name, config.length);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,mBAAmB,gCAAgC;AAYrD,MAAM,iCACJ,yBAIT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA4B;AACxD,UAAM,MAAM,UAAU,mBAAmB;AACzC,SAAK,OAAO,SAAS;AAAA,EACtB;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,0BAAqF,kBAGhG;AAAA,EACD,QAA0B,UAAU,IAAY;AAAA,EAEhD,SAA6B,KAAK,OAAO;AAAA,EAEhC,mBAAmB,OAA6C;AACxE,QAAI,OAAO,UAAU,SAAU,QAAO;AACtC,QAAI,OAAO,SAAS,KAAK,EAAG,QAAO,MAAM,SAAS;AAElD,UAAM,MAAgB,CAAC;AACvB,eAAW,KAAK,OAAO;AACtB,UAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAC9B;AAEA,WAAO,IAAI,KAAK,EAAE;AAAA,EACnB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,WAAW,UAAU,KAAK,MAAM;AAAA,EACpE;AACD;AAcO,SAAS,OAAO,GAAsC,IAA6B,CAAC,GAAG;AAC7F,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAgD,GAAG,CAAC;AAC7E,SAAO,IAAI,yBAAyB,MAAM,OAAO,MAAM;AACxD;", "names": []}
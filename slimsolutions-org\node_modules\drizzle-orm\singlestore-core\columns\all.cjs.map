{"version": 3, "sources": ["../../../src/singlestore-core/columns/all.ts"], "sourcesContent": ["import { bigint } from './bigint.ts';\nimport { binary } from './binary.ts';\nimport { boolean } from './boolean.ts';\nimport { char } from './char.ts';\nimport { customType } from './custom.ts';\nimport { date } from './date.ts';\nimport { datetime } from './datetime.ts';\nimport { decimal } from './decimal.ts';\nimport { double } from './double.ts';\nimport { singlestoreEnum } from './enum.ts';\nimport { float } from './float.ts';\nimport { int } from './int.ts';\nimport { json } from './json.ts';\nimport { mediumint } from './mediumint.ts';\nimport { real } from './real.ts';\nimport { serial } from './serial.ts';\nimport { smallint } from './smallint.ts';\nimport { longtext, mediumtext, text, tinytext } from './text.ts';\nimport { time } from './time.ts';\nimport { timestamp } from './timestamp.ts';\nimport { tinyint } from './tinyint.ts';\nimport { varbinary } from './varbinary.ts';\nimport { varchar } from './varchar.ts';\nimport { vector } from './vector.ts';\nimport { year } from './year.ts';\n\nexport function getSingleStoreColumnBuilders() {\n\treturn {\n\t\tbigint,\n\t\tbinary,\n\t\tboolean,\n\t\tchar,\n\t\tcustomType,\n\t\tdate,\n\t\tdatetime,\n\t\tdecimal,\n\t\tdouble,\n\t\tsinglestoreEnum,\n\t\tfloat,\n\t\tint,\n\t\tjson,\n\t\tmediumint,\n\t\treal,\n\t\tserial,\n\t\tsmallint,\n\t\tlongtext,\n\t\tmediumtext,\n\t\ttext,\n\t\ttinytext,\n\t\ttime,\n\t\ttimestamp,\n\t\ttinyint,\n\t\tvarbinary,\n\t\tvarchar,\n\t\tvector,\n\t\tyear,\n\t};\n}\n\nexport type SingleStoreColumnBuilders = ReturnType<typeof getSingleStoreColumnBuilders>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAuB;AACvB,oBAAuB;AACvB,qBAAwB;AACxB,kBAAqB;AACrB,oBAA2B;AAC3B,kBAAqB;AACrB,sBAAyB;AACzB,qBAAwB;AACxB,oBAAuB;AACvB,kBAAgC;AAChC,mBAAsB;AACtB,iBAAoB;AACpB,kBAAqB;AACrB,uBAA0B;AAC1B,kBAAqB;AACrB,oBAAuB;AACvB,sBAAyB;AACzB,kBAAqD;AACrD,kBAAqB;AACrB,uBAA0B;AAC1B,qBAAwB;AACxB,uBAA0B;AAC1B,qBAAwB;AACxB,oBAAuB;AACvB,kBAAqB;AAEd,SAAS,+BAA+B;AAC9C,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}
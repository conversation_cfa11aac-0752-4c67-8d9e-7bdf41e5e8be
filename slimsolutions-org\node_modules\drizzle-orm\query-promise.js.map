{"version": 3, "sources": ["../src/query-promise.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport abstract class QueryPromise<T> implements Promise<T> {\n\tstatic readonly [entityKind]: string = 'QueryPromise';\n\n\t[Symbol.toStringTag] = 'QueryPromise';\n\n\tcatch<TResult = never>(\n\t\tonRejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined,\n\t): Promise<T | TResult> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<T> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n\n\tthen<TResult1 = T, TResult2 = never>(\n\t\tonFulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n\t\tonRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn this.execute().then(onFulfilled, onRejected);\n\t}\n\n\tabstract execute(): Promise<T>;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAEpB,MAAe,aAAsC;AAAA,EAC3D,QAAiB,UAAU,IAAY;AAAA,EAEvC,CAAC,OAAO,WAAW,IAAI;AAAA,EAEvB,MACC,YACuB;AACvB,WAAO,KAAK,KAAK,QAAW,UAAU;AAAA,EACvC;AAAA,EAEA,QAAQ,WAAyD;AAChE,WAAO,KAAK;AAAA,MACX,CAAC,UAAU;AACV,oBAAY;AACZ,eAAO;AAAA,MACR;AAAA,MACA,CAAC,WAAW;AACX,oBAAY;AACZ,cAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AAAA,EAEA,KACC,aACA,YAC+B;AAC/B,WAAO,KAAK,QAAQ,EAAE,KAAK,aAAa,UAAU;AAAA,EACnD;AAGD;", "names": []}
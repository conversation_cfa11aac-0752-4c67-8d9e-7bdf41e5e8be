{"version": 3, "sources": ["../src/logger.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface Logger {\n\tlogQuery(query: string, params: unknown[]): void;\n}\n\nexport interface LogWriter {\n\twrite(message: string): void;\n}\n\nexport class ConsoleLogWriter implements LogWriter {\n\tstatic readonly [entityKind]: string = 'ConsoleLogWriter';\n\n\twrite(message: string) {\n\t\tconsole.log(message);\n\t}\n}\n\nexport class DefaultLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'DefaultLogger';\n\n\treadonly writer: LogWriter;\n\n\tconstructor(config?: { writer: LogWriter }) {\n\t\tthis.writer = config?.writer ?? new ConsoleLogWriter();\n\t}\n\n\tlogQuery(query: string, params: unknown[]): void {\n\t\tconst stringifiedParams = params.map((p) => {\n\t\t\ttry {\n\t\t\t\treturn JSON.stringify(p);\n\t\t\t} catch {\n\t\t\t\treturn String(p);\n\t\t\t}\n\t\t});\n\t\tconst paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(', ')}]` : '';\n\t\tthis.writer.write(`Query: ${query}${paramsStr}`);\n\t}\n}\n\nexport class NoopLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'NoopLogger';\n\n\tlogQuery(): void {\n\t\t// noop\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAUpB,MAAM,iBAAsC;AAAA,EAClD,QAAiB,UAAU,IAAY;AAAA,EAEvC,MAAM,SAAiB;AACtB,YAAQ,IAAI,OAAO;AAAA,EACpB;AACD;AAEO,MAAM,cAAgC;AAAA,EAC5C,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EAET,YAAY,QAAgC;AAC3C,SAAK,SAAS,QAAQ,UAAU,IAAI,iBAAiB;AAAA,EACtD;AAAA,EAEA,SAAS,OAAe,QAAyB;AAChD,UAAM,oBAAoB,OAAO,IAAI,CAAC,MAAM;AAC3C,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;AAAA,MACxB,QAAQ;AACP,eAAO,OAAO,CAAC;AAAA,MAChB;AAAA,IACD,CAAC;AACD,UAAM,YAAY,kBAAkB,SAAS,gBAAgB,kBAAkB,KAAK,IAAI,CAAC,MAAM;AAC/F,SAAK,OAAO,MAAM,UAAU,KAAK,GAAG,SAAS,EAAE;AAAA,EAChD;AACD;AAEO,MAAM,WAA6B;AAAA,EACzC,QAAiB,UAAU,IAAY;AAAA,EAEvC,WAAiB;AAAA,EAEjB;AACD;", "names": []}
{"version": 3, "sources": ["../../../src/pg-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgRealBuilderInitial<TName extends string> = PgRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgReal';\n\tdata: number;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class PgRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgReal'>> extends PgColumnBuilder<\n\tT,\n\t{ length: number | undefined }\n> {\n\tstatic override readonly [entityKind]: string = 'PgRealBuilder';\n\n\tconstructor(name: T['name'], length?: number) {\n\t\tsuper(name, 'number', 'PgReal');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgReal<T extends ColumnBaseConfig<'number', 'PgReal'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgReal';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgRealBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'real';\n\t}\n\n\toverride mapFromDriverValue = (value: string | number): number => {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseFloat(value);\n\t\t}\n\t\treturn value;\n\t};\n}\n\nexport function real(): PgRealBuilderInitial<''>;\nexport function real<TName extends string>(name: TName): PgRealBuilderInitial<TName>;\nexport function real(name?: string) {\n\treturn new PgRealBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAWnC,MAAM,sBAA6E,8BAGxF;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAiB;AAC7C,UAAM,MAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,SAAS;AAAA,EACtB;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eAA+D,uBAAY;AAAA,EACvF,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,OAA6C,QAAoC;AAC5F,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,qBAAqB,CAAC,UAAmC;AACjE,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,WAAW,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,cAAc,QAAQ,EAAE;AACpC;", "names": []}
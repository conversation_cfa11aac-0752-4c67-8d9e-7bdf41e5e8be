{"version": 3, "sources": ["../../../src/mysql-core/columns/bigint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlBigInt53BuilderInitial<TName extends string> = MySqlBigInt53Builder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlBigInt53';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlBigInt53Builder<T extends ColumnBuilderBaseConfig<'number', 'MySqlBigInt53'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlBigInt53Builder';\n\n\tconstructor(name: T['name'], unsigned: boolean = false) {\n\t\tsuper(name, 'number', 'MySqlBigInt53');\n\t\tthis.config.unsigned = unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBigInt53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBigInt53<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlBigInt53<T extends ColumnBaseConfig<'number', 'MySqlBigInt53'>>\n\textends MySqlColumnWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlBigInt53';\n\n\tgetSQLType(): string {\n\t\treturn `bigint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'number') {\n\t\t\treturn value;\n\t\t}\n\t\treturn Number(value);\n\t}\n}\n\nexport type MySqlBigInt64BuilderInitial<TName extends string> = MySqlBigInt64Builder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'MySqlBigInt64';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlBigInt64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'MySqlBigInt64'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlBigInt64Builder';\n\n\tconstructor(name: T['name'], unsigned: boolean = false) {\n\t\tsuper(name, 'bigint', 'MySqlBigInt64');\n\t\tthis.config.unsigned = unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBigInt64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBigInt64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlBigInt64<T extends ColumnBaseConfig<'bigint', 'MySqlBigInt64'>>\n\textends MySqlColumnWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlBigInt64';\n\n\tgetSQLType(): string {\n\t\treturn `bigint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\t// eslint-disable-next-line unicorn/prefer-native-coercion-functions\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value);\n\t}\n}\n\nexport interface MySqlBigIntConfig<T extends 'number' | 'bigint' = 'number' | 'bigint'> {\n\tmode: T;\n\tunsigned?: boolean;\n}\n\nexport function bigint<TMode extends MySqlBigIntConfig['mode']>(\n\tconfig: MySqlBigIntConfig<TMode>,\n): TMode extends 'number' ? MySqlBigInt53BuilderInitial<''> : MySqlBigInt64BuilderInitial<''>;\nexport function bigint<TName extends string, TMode extends MySqlBigIntConfig['mode']>(\n\tname: TName,\n\tconfig: MySqlBigIntConfig<TMode>,\n): TMode extends 'number' ? MySqlBigInt53BuilderInitial<TName> : MySqlBigInt64BuilderInitial<TName>;\nexport function bigint(a?: string | MySqlBigIntConfig, b?: MySqlBigIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlBigIntConfig>(a, b);\n\tif (config.mode === 'number') {\n\t\treturn new MySqlBigInt53Builder(name, config.unsigned);\n\t}\n\treturn new MySqlBigInt64Builder(name, config.unsigned);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAAkF;AAW3E,MAAM,6BACJ,kDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAO;AACvD,UAAM,MAAM,UAAU,eAAe;AACrC,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,SAAS,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACxD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;AAAA,IACR;AACA,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAWO,MAAM,6BACJ,kDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAO;AACvD,UAAM,MAAM,UAAU,eAAe;AACrC,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,SAAS,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACxD;AAAA;AAAA,EAGS,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAcO,SAAS,OAAO,GAAgC,GAAuB;AAC7E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA0C,GAAG,CAAC;AACvE,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,qBAAqB,MAAM,OAAO,QAAQ;AAAA,EACtD;AACA,SAAO,IAAI,qBAAqB,MAAM,OAAO,QAAQ;AACtD;", "names": []}
{"version": 3, "sources": ["../../src/sqlite-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { getSQLiteColumnBuilders, type SQLiteColumnBuilders } from './columns/all.ts';\nimport type { SQLiteColumn, SQLiteColumnBuilder, SQLiteColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { IndexBuilder } from './indexes.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type SQLiteTableExtraConfigValue =\n\t| IndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder;\n\nexport type SQLiteTableExtraConfig = Record<\n\tstring,\n\tSQLiteTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<SQLiteColumn<any>>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:SQLiteInlineForeignKeys');\n\nexport class SQLiteTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t});\n\n\t/** @internal */\n\toverride [Table.Symbol.Columns]!: NonNullable<T['columns']>;\n\n\t/** @internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]:\n\t\t| ((self: Record<string, SQLiteColumn>) => SQLiteTableExtraConfig)\n\t\t| undefined = undefined;\n}\n\nexport type AnySQLiteTable<TPartial extends Partial<TableConfig> = {}> = SQLiteTable<\n\tUpdateTableConfig<TableConfig, TPartial>\n>;\n\nexport type SQLiteTableWithColumns<T extends TableConfig> =\n\t& SQLiteTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t};\n\nexport interface SQLiteTableFn<TSchema extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'sqlite'>,\n\t\t) => SQLiteTableExtraConfigValue[],\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SQLiteColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfigValue[],\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of sqliteTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfig,\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of sqliteTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SQLiteColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfig,\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n}\n\nfunction sqliteTableBase<\n\tTTableName extends string,\n\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\tTSchema extends string | undefined,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: SQLiteColumnBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'sqlite'>,\n\t\t) => SQLiteTableExtraConfig | SQLiteTableExtraConfigValue[])\n\t\t| undefined,\n\tschema?: TSchema,\n\tbaseName = name,\n): SQLiteTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchema;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\tdialect: 'sqlite';\n}> {\n\tconst rawTable = new SQLiteTable<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getSQLiteColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as SQLiteColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumns as unknown as BuildExtraConfigColumns<\n\t\tTTableName,\n\t\tTColumnsMap,\n\t\t'sqlite'\n\t>;\n\n\tif (extraConfig) {\n\t\ttable[SQLiteTable.Symbol.ExtraConfigBuilder] = extraConfig as (\n\t\t\tself: Record<string, SQLiteColumn>,\n\t\t) => SQLiteTableExtraConfig;\n\t}\n\n\treturn table;\n}\n\nexport const sqliteTable: SQLiteTableFn = (name, columns, extraConfig) => {\n\treturn sqliteTableBase(name, columns, extraConfig);\n};\n\nexport function sqliteTableCreator(customizeTableName: (name: string) => string): SQLiteTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn sqliteTableBase(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,aAA0E;AAEnF,SAAS,+BAA0D;AAsB5D,MAAM,oBAAoB,OAAO,IAAI,iCAAiC;AAEtE,MAAM,oBAAyD,MAAS;AAAA,EAC9E,QAA0B,UAAU,IAAY;AAAA;AAAA,EAGhD,OAAyB,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IACjE;AAAA,EACD,CAAC;AAAA;AAAA,EAGD,CAAU,MAAM,OAAO,OAAO;AAAA;AAAA,EAG9B,CAAC,iBAAiB,IAAkB,CAAC;AAAA;AAAA,EAGrC,CAAU,MAAM,OAAO,kBAAkB,IAE1B;AAChB;AAmHA,SAAS,gBAKR,MACA,SACA,aAKA,QACA,WAAW,MAMT;AACF,QAAM,WAAW,IAAI,YAKlB,MAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,QAAQ,wBAAwB,CAAC,IAAI;AAExG,QAAM,eAAe,OAAO;AAAA,IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,eAAS,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,QAAQ,QAAQ,CAAC;AACjF,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,MAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,MAAM,OAAO,kBAAkB,IAAI;AAMzC,MAAI,aAAa;AAChB,UAAM,YAAY,OAAO,kBAAkB,IAAI;AAAA,EAGhD;AAEA,SAAO;AACR;AAEO,MAAM,cAA6B,CAAC,MAAM,SAAS,gBAAgB;AACzE,SAAO,gBAAgB,MAAM,SAAS,WAAW;AAClD;AAEO,SAAS,mBAAmB,oBAA6D;AAC/F,SAAO,CAAC,MAAM,SAAS,gBAAgB;AACtC,WAAO,gBAAgB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,QAAW,IAAI;AAAA,EACtG;AACD;", "names": ["name"]}
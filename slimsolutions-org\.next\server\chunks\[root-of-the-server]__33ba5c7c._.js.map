{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, serial, varchar, text, timestamp, boolean, integer, decimal, jsonb } from 'drizzle-orm/pg-core';\n\n// Users table for email subscribers and customers\nexport const users = pgTable('users', {\n  id: serial('id').primaryKey(),\n  email: varchar('email', { length: 255 }).notNull().unique(),\n  firstName: varchar('first_name', { length: 100 }),\n  lastName: varchar('last_name', { length: 100 }),\n  weightGoal: varchar('weight_goal', { length: 50 }), // '5-10 lbs', '10-25 lbs', etc.\n  isSubscribed: boolean('is_subscribed').default(true),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Articles/Blog posts table\nexport const articles = pgTable('articles', {\n  id: serial('id').primaryKey(),\n  title: varchar('title', { length: 255 }).notNull(),\n  slug: varchar('slug', { length: 255 }).notNull().unique(),\n  excerpt: text('excerpt'),\n  content: text('content').notNull(),\n  metaTitle: varchar('meta_title', { length: 255 }),\n  metaDescription: text('meta_description'),\n  keywords: text('keywords'), // comma-separated keywords\n  featuredImage: varchar('featured_image', { length: 500 }),\n  authorName: varchar('author_name', { length: 100 }).default('SlimSolutions Team'),\n  isPublished: boolean('is_published').default(false),\n  publishedAt: timestamp('published_at'),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Categories for articles\nexport const categories = pgTable('categories', {\n  id: serial('id').primaryKey(),\n  name: varchar('name', { length: 100 }).notNull(),\n  slug: varchar('slug', { length: 100 }).notNull().unique(),\n  description: text('description'),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Article categories junction table\nexport const articleCategories = pgTable('article_categories', {\n  id: serial('id').primaryKey(),\n  articleId: integer('article_id').references(() => articles.id),\n  categoryId: integer('category_id').references(() => categories.id),\n});\n\n// Affiliate products table\nexport const affiliateProducts = pgTable('affiliate_products', {\n  id: serial('id').primaryKey(),\n  name: varchar('name', { length: 255 }).notNull(),\n  description: text('description'),\n  price: decimal('price', { precision: 10, scale: 2 }),\n  originalPrice: decimal('original_price', { precision: 10, scale: 2 }),\n  affiliateUrl: varchar('affiliate_url', { length: 500 }).notNull(),\n  vendor: varchar('vendor', { length: 100 }), // 'ClickBank', etc.\n  productId: varchar('product_id', { length: 100 }), // vendor product ID\n  commission: decimal('commission', { precision: 5, scale: 2 }), // percentage\n  imageUrl: varchar('image_url', { length: 500 }),\n  rating: decimal('rating', { precision: 3, scale: 2 }), // 0.00 to 5.00\n  isActive: boolean('is_active').default(true),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Email campaigns table\nexport const emailCampaigns = pgTable('email_campaigns', {\n  id: serial('id').primaryKey(),\n  name: varchar('name', { length: 255 }).notNull(),\n  subject: varchar('subject', { length: 255 }).notNull(),\n  content: text('content').notNull(),\n  templateId: varchar('template_id', { length: 100 }),\n  status: varchar('status', { length: 50 }).default('draft'), // draft, scheduled, sent\n  scheduledAt: timestamp('scheduled_at'),\n  sentAt: timestamp('sent_at'),\n  recipientCount: integer('recipient_count').default(0),\n  openRate: decimal('open_rate', { precision: 5, scale: 2 }),\n  clickRate: decimal('click_rate', { precision: 5, scale: 2 }),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Analytics table for tracking clicks, conversions, etc.\nexport const analytics = pgTable('analytics', {\n  id: serial('id').primaryKey(),\n  eventType: varchar('event_type', { length: 50 }).notNull(), // 'page_view', 'click', 'conversion', etc.\n  eventData: jsonb('event_data'), // flexible data storage\n  userId: integer('user_id').references(() => users.id),\n  sessionId: varchar('session_id', { length: 255 }),\n  ipAddress: varchar('ip_address', { length: 45 }),\n  userAgent: text('user_agent'),\n  referrer: varchar('referrer', { length: 500 }),\n  page: varchar('page', { length: 500 }),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Contact form submissions\nexport const contactSubmissions = pgTable('contact_submissions', {\n  id: serial('id').primaryKey(),\n  name: varchar('name', { length: 100 }).notNull(),\n  email: varchar('email', { length: 255 }).notNull(),\n  subject: varchar('subject', { length: 255 }),\n  message: text('message').notNull(),\n  isRead: boolean('is_read').default(false),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Newsletter subscriptions tracking\nexport const newsletterSubscriptions = pgTable('newsletter_subscriptions', {\n  id: serial('id').primaryKey(),\n  email: varchar('email', { length: 255 }).notNull().unique(),\n  source: varchar('source', { length: 100 }), // 'homepage', 'article', 'popup', etc.\n  isActive: boolean('is_active').default(true),\n  confirmedAt: timestamp('confirmed_at'),\n  unsubscribedAt: timestamp('unsubscribed_at'),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGO,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,QAAQ;IAAI;IAC7C,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAG;IAChD,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC/C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO;IAChD,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACvD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,iBAAiB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACtB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;QAAE,QAAQ;IAAI;IACvD,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAI,GAAG,OAAO,CAAC;IAC5D,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC9C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACvD,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;IAC7D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,UAAU,CAAC,IAAM,WAAW,EAAE;AACnE;AAGO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;IAC7D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAI,OAAO;IAAE;IAClD,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;QAAE,WAAW;QAAI,OAAO;IAAE;IACnE,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC/D,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAI;IACxC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,WAAW;QAAG,OAAO;IAAE;IAC3D,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,QAAQ;IAAI;IAC7C,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,WAAW;QAAG,OAAO;IAAE;IACnD,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,iBAAiB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB;IACvD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAAE,QAAQ;IAAI,GAAG,OAAO;IACpD,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAI;IACjD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAG,GAAG,OAAO,CAAC;IAClD,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,QAAQ,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAClB,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,OAAO,CAAC;IACnD,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,WAAW;QAAG,OAAO;IAAE;IACxD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,WAAW;QAAG,OAAO;IAAE;IAC1D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAC5C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAG,GAAG,OAAO;IACxD,WAAW,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;IACjB,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,UAAU,CAAC,IAAM,MAAM,EAAE;IACpD,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAC/C,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAG;IAC9C,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI;IAC5C,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI;IACpC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB;IAC/D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO;IAChD,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAAE,QAAQ;IAAI;IAC1C,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC;IACnC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C;AAGO,MAAM,0BAA0B,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,4BAA4B;IACzE,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAI;IACxC,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,gBAAgB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAC1B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU;AAC/C", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/lib/db/connection.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\n// Create connection pool\nconst pool = new Pool({\n  host: process.env.POSTGRES_HOST,\n  port: parseInt(process.env.POSTGRES_PORT || '5432'),\n  user: process.env.POSTGRES_USER,\n  password: process.env.POSTGRES_PASSWORD,\n  database: process.env.POSTGRES_DATABASE,\n  ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,\n  max: 20,\n  idleTimeoutMillis: 30000,\n  connectionTimeoutMillis: 2000,\n});\n\n// Create drizzle instance\nexport const db = drizzle(pool, { schema });\n\n// Test connection function\nexport async function testConnection() {\n  try {\n    const client = await pool.connect();\n    const result = await client.query('SELECT NOW()');\n    client.release();\n    console.log('Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n\n// Close connection pool\nexport async function closeConnection() {\n  await pool.end();\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;;;;AAEA,yBAAyB;AACzB,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,MAAM,QAAQ,GAAG,CAAC,aAAa;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,MAAM,QAAQ,GAAG,CAAC,aAAa;IAC/B,UAAU,QAAQ,GAAG,CAAC,iBAAiB;IACvC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;IACvC,KAAK,QAAQ,GAAG,CAAC,YAAY,KAAK,SAAS;QAAE,oBAAoB;IAAM,IAAI;IAC3E,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO;AAGlC,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;QAClC,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC,OAAO,IAAI,CAAC,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,KAAK,GAAG;AAChB", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/app/api/newsletter/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { db } from '@/lib/db/connection';\nimport { newsletterSubscriptions } from '@/lib/db/schema';\nimport { eq } from 'drizzle-orm';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, source = 'homepage' } = await request.json();\n\n    if (!email || !email.includes('@')) {\n      return NextResponse.json(\n        { error: 'Valid email address is required' },\n        { status: 400 }\n      );\n    }\n\n    // Insert newsletter subscription\n    const result = await db\n      .insert(newsletterSubscriptions)\n      .values({\n        email: email.toLowerCase(),\n        source,\n        isActive: true,\n        confirmedAt: new Date(),\n      })\n      .returning();\n\n    return NextResponse.json({\n      success: true,\n      message: 'Successfully subscribed to newsletter!',\n      subscription: result[0],\n    });\n  } catch (error) {\n    console.error('Newsletter subscription error:', error);\n\n    // Handle duplicate email\n    if (error.code === '23505') {\n      return NextResponse.json({\n        success: true,\n        message: 'You are already subscribed to our newsletter!',\n      });\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to subscribe to newsletter' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  try {\n    const count = await db\n      .select()\n      .from(newsletterSubscriptions)\n      .where(eq(newsletterSubscriptions.isActive, true));\n\n    return NextResponse.json({\n      success: true,\n      activeSubscriptions: count.length,\n    });\n  } catch (error) {\n    console.error('Newsletter count error:', error);\n    return NextResponse.json(\n      { error: 'Failed to get subscription count' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,SAAS,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,gIAAA,CAAA,KAAE,CACpB,MAAM,CAAC,4HAAA,CAAA,0BAAuB,EAC9B,MAAM,CAAC;YACN,OAAO,MAAM,WAAW;YACxB;YACA,UAAU;YACV,aAAa,IAAI;QACnB,GACC,SAAS;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,cAAc,MAAM,CAAC,EAAE;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,yBAAyB;QACzB,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,gIAAA,CAAA,KAAE,CACnB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,0BAAuB,EAC5B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,0BAAuB,CAAC,QAAQ,EAAE;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,qBAAqB,MAAM,MAAM;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAmC,GAC5C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
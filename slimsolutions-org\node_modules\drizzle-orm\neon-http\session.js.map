{"version": 3, "sources": ["../../src/neon-http/session.ts"], "sourcesContent": ["import type { FullQueryResults, NeonQueryFunction, NeonQueryPromise } from '@neondatabase/serverless';\nimport type { BatchItem } from '~/batch.ts';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery as PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { fillPlaceholders, type Query, type SQL } from '~/sql/sql.ts';\nimport { mapResultRow, type NeonAuthToken } from '~/utils.ts';\n\nexport type NeonHttpClient = NeonQueryFunction<any, any>;\n\nconst rawQueryConfig = {\n\tarrayMode: false,\n\tfullResults: true,\n} as const;\nconst queryConfig = {\n\tarrayMode: true,\n\tfullResults: true,\n} as const;\n\nexport class NeonHttpPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'NeonHttpPreparedQuery';\n\tprivate clientQuery: (sql: string, params: any[], opts: Record<string, any>) => NeonQueryPromise<any, any>;\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper(query, cache, queryMetadata, cacheConfig);\n\t\t// `client.query` is for @neondatabase/serverless v1.0.0 and up, where the\n\t\t// root query function `client` is only usable as a template function;\n\t\t// `client` is a fallback for earlier versions\n\t\tthis.clientQuery = (client as any).query ?? client as any;\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined): Promise<T['execute']>;\n\t/** @internal */\n\tasync execute(placeholderValues: Record<string, unknown> | undefined, token?: NeonAuthToken): Promise<T['execute']>;\n\t/** @internal */\n\tasync execute(\n\t\tplaceholderValues: Record<string, unknown> | undefined = {},\n\t\ttoken: NeonAuthToken | undefined = this.authToken,\n\t): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, clientQuery, query, customResultMapper } = this;\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn clientQuery(\n\t\t\t\t\tquery.sql,\n\t\t\t\t\tparams,\n\t\t\t\t\ttoken === undefined\n\t\t\t\t\t\t? rawQueryConfig\n\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t...rawQueryConfig,\n\t\t\t\t\t\t\tauthToken: token,\n\t\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t});\n\t\t}\n\n\t\tconst result = await this.queryWithCache(query.sql, params, async () => {\n\t\t\treturn await clientQuery(\n\t\t\t\tquery.sql,\n\t\t\t\tparams,\n\t\t\t\ttoken === undefined\n\t\t\t\t\t? queryConfig\n\t\t\t\t\t: {\n\t\t\t\t\t\t...queryConfig,\n\t\t\t\t\t\tauthToken: token,\n\t\t\t\t\t},\n\t\t\t);\n\t\t});\n\n\t\treturn this.mapResult(result);\n\t}\n\n\toverride mapResult(result: unknown): unknown {\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst rows = (result as FullQueryResults<true>).rows;\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow(this.fields!, row, this.joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.clientQuery(\n\t\t\tthis.query.sql,\n\t\t\tparams,\n\t\t\tthis.authToken === undefined ? rawQueryConfig : {\n\t\t\t\t...rawQueryConfig,\n\t\t\t\tauthToken: this.authToken,\n\t\t\t},\n\t\t).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined): Promise<T['values']>;\n\t/** @internal */\n\tvalues(placeholderValues: Record<string, unknown> | undefined, token?: NeonAuthToken): Promise<T['values']>;\n\t/** @internal */\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}, token?: NeonAuthToken): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.clientQuery(this.query.sql, params, { arrayMode: true, fullResults: true, authToken: token }).then((\n\t\t\tresult,\n\t\t) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface NeonHttpSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class NeonHttpSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NeonHttpQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonHttpSession';\n\n\tprivate clientQuery: (sql: string, params: any[], opts: Record<string, any>) => NeonQueryPromise<any, any>;\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NeonHttpSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\t// `client.query` is for @neondatabase/serverless v1.0.0 and up, where the\n\t\t// root query function `client` is only usable as a template function;\n\t\t// `client` is a fallback for earlier versions\n\t\tthis.clientQuery = (client as any).query ?? client as any;\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T> {\n\t\treturn new NeonHttpPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync batch<U extends BatchItem<'pg'>, T extends Readonly<[U, ...U[]]>>(\n\t\tqueries: T,\n\t) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: NeonQueryPromise<any, true>[] = [];\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tbuiltQueries.push(\n\t\t\t\tthis.clientQuery(builtQuery.sql, builtQuery.params, {\n\t\t\t\t\tfullResults: true,\n\t\t\t\t\tarrayMode: preparedQuery.isResponseInArrayMode(),\n\t\t\t\t}),\n\t\t\t);\n\t\t}\n\n\t\tconst batchResults = await this.client.transaction(builtQueries, queryConfig);\n\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true)) as any;\n\t}\n\n\t// change return type to QueryRows<true>\n\tasync query(query: string, params: unknown[]): Promise<FullQueryResults<true>> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.clientQuery(query, params, { arrayMode: true, fullResults: true });\n\t\treturn result;\n\t}\n\n\t// change return type to QueryRows<false>\n\tasync queryObjects(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<FullQueryResults<false>> {\n\t\treturn this.clientQuery(query, params, { arrayMode: false, fullResults: true });\n\t}\n\n\toverride async count(sql: SQL): Promise<number>;\n\t/** @internal */\n\toverride async count(sql: SQL, token?: NeonAuthToken): Promise<number>;\n\t/** @internal */\n\toverride async count(sql: SQL, token?: NeonAuthToken): Promise<number> {\n\t\tconst res = await this.execute<{ rows: [{ count: string }] }>(sql, token);\n\n\t\treturn Number(\n\t\t\tres['rows'][0]['count'],\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t\t_config: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tthrow new Error('No transactions support in neon-http driver');\n\t}\n}\n\nexport class NeonTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NeonHttpQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonHttpTransaction';\n\n\toverride async transaction<T>(_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tthrow new Error('No transactions support in neon-http driver');\n\t\t// const savepointName = `sp${this.nestedIndex + 1}`;\n\t\t// const tx = new NeonTransaction(this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\t// await tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\t// try {\n\t\t// \tconst result = await transaction(tx);\n\t\t// \tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t// \treturn result;\n\t\t// } catch (e) {\n\t\t// \tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t// \tthrow e;\n\t\t// }\n\t}\n}\n\nexport type NeonHttpQueryResult<T> = Omit<FullQueryResults<false>, 'rows'> & { rows: T[] };\n\nexport interface NeonHttpQueryResultHKT extends PgQueryResultHKT {\n\ttype: NeonHttpQueryResult<this['row']>;\n}\n"], "mappings": "AAEA,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAG9B,SAAS,iBAAoC,iBAAiB;AAG9D,SAAS,wBAA8C;AACvD,SAAS,oBAAwC;AAIjD,MAAM,iBAAiB;AAAA,EACtB,WAAW;AAAA,EACX,aAAa;AACd;AACA,MAAM,cAAc;AAAA,EACnB,WAAW;AAAA,EACX,aAAa;AACd;AAEO,MAAM,8BAA6D,gBAAmB;AAAA,EAI5F,YACS,QACR,OACQ,QACR,OACA,eAIA,aACQ,QACA,wBACA,oBACP;AACD,UAAM,OAAO,OAAO,eAAe,WAAW;AAbtC;AAEA;AAOA;AACA;AACA;AAMR,SAAK,cAAe,OAAe,SAAS;AAAA,EAC7C;AAAA,EAtBA,QAA0B,UAAU,IAAY;AAAA,EACxC;AAAA;AAAA,EA2BR,MAAM,QACL,oBAAyD,CAAC,GAC1D,QAAmC,KAAK,WAChB;AACxB,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AAEpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,aAAa,OAAO,mBAAmB,IAAI;AAE3D,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AACzD,eAAO;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,UAAU,SACP,iBACA;AAAA,YACD,GAAG;AAAA,YACH,WAAW;AAAA,UACZ;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAEA,UAAM,SAAS,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AACvE,aAAO,MAAM;AAAA,QACZ,MAAM;AAAA,QACN;AAAA,QACA,UAAU,SACP,cACA;AAAA,UACD,GAAG;AAAA,UACH,WAAW;AAAA,QACZ;AAAA,MACF;AAAA,IACD,CAAC;AAED,WAAO,KAAK,UAAU,MAAM;AAAA,EAC7B;AAAA,EAES,UAAU,QAA0B;AAC5C,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,UAAM,OAAQ,OAAkC;AAEhD,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,IAAI;AAAA,IACpC;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAAa,KAAK,QAAS,KAAK,KAAK,mBAAmB,CAAC;AAAA,EACnF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK;AAAA,MACX,KAAK,MAAM;AAAA,MACX;AAAA,MACA,KAAK,cAAc,SAAY,iBAAiB;AAAA,QAC/C,GAAG;AAAA,QACH,WAAW,KAAK;AAAA,MACjB;AAAA,IACD,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAC/B;AAAA;AAAA,EAMA,OAAO,oBAAyD,CAAC,GAAG,OAA6C;AAChH,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,YAAY,KAAK,MAAM,KAAK,QAAQ,EAAE,WAAW,MAAM,aAAa,MAAM,WAAW,MAAM,CAAC,EAAE,KAAK,CAC9G,WACI,OAAO,IAAI;AAAA,EACjB;AAAA;AAAA,EAGA,wBAAwB;AACvB,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,wBAGH,UAAwD;AAAA,EAOjE,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAMR,SAAK,cAAe,OAAe,SAAS;AAC5C,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAnBA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EACA;AAAA,EAiBR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MACL,SACC;AACD,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAA8C,CAAC;AACrD,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAa,cAAc,SAAS;AAC1C,sBAAgB,KAAK,aAAa;AAClC,mBAAa;AAAA,QACZ,KAAK,YAAY,WAAW,KAAK,WAAW,QAAQ;AAAA,UACnD,aAAa;AAAA,UACb,WAAW,cAAc,sBAAsB;AAAA,QAChD,CAAC;AAAA,MACF;AAAA,IACD;AAEA,UAAM,eAAe,MAAM,KAAK,OAAO,YAAY,cAAc,WAAW;AAE5E,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA;AAAA,EAGA,MAAM,MAAM,OAAe,QAAoD;AAC9E,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,YAAY,OAAO,QAAQ,EAAE,WAAW,MAAM,aAAa,KAAK,CAAC;AAC3F,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,aACL,OACA,QACmC;AACnC,WAAO,KAAK,YAAY,OAAO,QAAQ,EAAE,WAAW,OAAO,aAAa,KAAK,CAAC;AAAA,EAC/E;AAAA;AAAA,EAMA,MAAe,MAAM,KAAU,OAAwC;AACtE,UAAM,MAAM,MAAM,KAAK,QAAuC,KAAK,KAAK;AAExE,WAAO;AAAA,MACN,IAAI,MAAM,EAAE,CAAC,EAAE,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EAEA,MAAe,YACd,cAEA,UAA+B,CAAC,GACnB;AACb,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;AAEO,MAAM,wBAGH,cAA4D;AAAA,EACrE,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,cAAqF;AAClH,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAY9D;AACD;", "names": []}
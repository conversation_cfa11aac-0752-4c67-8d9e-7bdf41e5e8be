{"version": 3, "sources": ["../../../src/singlestore-core/columns/date.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreDateBuilderInitial<TName extends string> = SingleStoreDateBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'SingleStoreDate';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDateBuilder<T extends ColumnBuilderBaseConfig<'date', 'SingleStoreDate'>>\n\textends SingleStoreColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'SingleStoreDate');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDate<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDate<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDate<T extends ColumnBaseConfig<'date', 'SingleStoreDate'>> extends SingleStoreColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDate';\n\n\tconstructor(\n\t\ttable: AnySingleStoreTable<{ name: T['tableName'] }>,\n\t\tconfig: SingleStoreDateBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `date`;\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value);\n\t}\n}\n\nexport type SingleStoreDateStringBuilderInitial<TName extends string> = SingleStoreDateStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreDateString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDateStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreDateString'>>\n\textends SingleStoreColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SingleStoreDateString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDateString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDateString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDateString<T extends ColumnBaseConfig<'string', 'SingleStoreDateString'>>\n\textends SingleStoreColumn<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateString';\n\n\tconstructor(\n\t\ttable: AnySingleStoreTable<{ name: T['tableName'] }>,\n\t\tconfig: SingleStoreDateStringBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `date`;\n\t}\n}\n\nexport interface SingleStoreDateConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n}\n\nexport function date(): SingleStoreDateBuilderInitial<''>;\nexport function date<TMode extends SingleStoreDateConfig['mode'] & {}>(\n\tconfig?: SingleStoreDateConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreDateStringBuilderInitial<''> : SingleStoreDateBuilderInitial<''>;\nexport function date<TName extends string, TMode extends SingleStoreDateConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: SingleStoreDateConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreDateStringBuilderInitial<TName>\n\t: SingleStoreDateBuilderInitial<TName>;\nexport function date(a?: string | SingleStoreDateConfig, b?: SingleStoreDateConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreDateConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new SingleStoreDateStringBuilder(name);\n\t}\n\treturn new SingleStoreDateBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA8B;AACnD,SAAS,mBAAmB,gCAAgC;AAYrD,MAAM,+BACJ,yBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,iBAAiB;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAA+E,kBAAqB;AAAA,EAChH,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK;AAAA,EACtB;AACD;AAYO,MAAM,qCACJ,yBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,uBAAuB;AAAA,EAC9C;AAAA;AAAA,EAGS,MACR,OACyD;AACzD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,8BACJ,kBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAeO,SAAS,KAAK,GAAoC,GAA2B;AACnF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA0D,GAAG,CAAC;AACvF,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,6BAA6B,IAAI;AAAA,EAC7C;AACA,SAAO,IAAI,uBAAuB,IAAI;AACvC;", "names": []}
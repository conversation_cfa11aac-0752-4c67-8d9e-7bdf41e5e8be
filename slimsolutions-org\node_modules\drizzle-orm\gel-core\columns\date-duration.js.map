{"version": 3, "sources": ["../../../src/gel-core/columns/date-duration.ts"], "sourcesContent": ["import type { DateDuration } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelDateDurationBuilderInitial<TName extends string> = GelDateDurationBuilder<{\n\tname: TName;\n\tdataType: 'dateDuration';\n\tcolumnType: 'GelDateDuration';\n\tdata: DateDuration;\n\tdriverParam: DateDuration;\n\tenumValues: undefined;\n}>;\n\nexport class GelDateDurationBuilder<T extends ColumnBuilderBaseConfig<'dateDuration', 'GelDateDuration'>>\n\textends GelColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelDateDurationBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'dateDuration', 'GelDateDuration');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelDateDuration<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelDateDuration<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelDateDuration<T extends ColumnBaseConfig<'dateDuration', 'GelDateDuration'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelDateDuration';\n\n\tgetSQLType(): string {\n\t\treturn `dateDuration`;\n\t}\n}\n\nexport function dateDuration(): GelDateDurationBuilderInitial<''>;\nexport function dateDuration<TName extends string>(name: TName): GelDateDurationBuilderInitial<TName>;\nexport function dateDuration(name?: string) {\n\treturn new GelDateDurationBuilder(name ?? '');\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,+BACJ,iBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,gBAAgB,iBAAiB;AAAA,EAC9C;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAAuF,UAAa;AAAA,EAChH,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,aAAa,MAAe;AAC3C,SAAO,IAAI,uBAAuB,QAAQ,EAAE;AAC7C;", "names": []}
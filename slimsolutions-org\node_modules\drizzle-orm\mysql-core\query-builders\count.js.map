{"version": 3, "sources": ["../../../src/mysql-core/query-builders/count.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { SQL, sql, type S<PERSON>Wrapper } from '~/sql/sql.ts';\nimport type { MySqlSession } from '../session.ts';\nimport type { MySqlTable } from '../table.ts';\nimport type { MySqlViewBase } from '../view-base.ts';\n\nexport class MySqlCountBuilder<\n\tTSession extends MySqlSession<any, any, any>,\n> extends SQL<number> implements Promise<number>, SQLWrapper {\n\tprivate sql: SQL<number>;\n\n\tstatic override readonly [entityKind] = 'MySqlCountBuilder';\n\t[Symbol.toStringTag] = 'MySqlCountBuilder';\n\n\tprivate session: TSession;\n\n\tprivate static buildEmbeddedCount(\n\t\tsource: MySqlTable | MySqlViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`(select count(*) from ${source}${sql.raw(' where ').if(filters)}${filters})`;\n\t}\n\n\tprivate static buildCount(\n\t\tsource: MySqlTable | MySqlViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`select count(*) as count from ${source}${sql.raw(' where ').if(filters)}${filters}`;\n\t}\n\n\tconstructor(\n\t\treadonly params: {\n\t\t\tsource: MySqlTable | MySqlViewBase | SQL | SQLWrapper;\n\t\t\tfilters?: SQL<unknown>;\n\t\t\tsession: TSession;\n\t\t},\n\t) {\n\t\tsuper(MySqlCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n\n\t\tthis.mapWith(Number);\n\n\t\tthis.session = params.session;\n\n\t\tthis.sql = MySqlCountBuilder.buildCount(\n\t\t\tparams.source,\n\t\t\tparams.filters,\n\t\t);\n\t}\n\n\tthen<TResult1 = number, TResult2 = never>(\n\t\tonfulfilled?: ((value: number) => TResult1 | PromiseLike<TResult1>) | null | undefined,\n\t\tonrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn Promise.resolve(this.session.count(this.sql))\n\t\t\t.then(\n\t\t\t\tonfulfilled,\n\t\t\t\tonrejected,\n\t\t\t);\n\t}\n\n\tcatch(\n\t\tonRejected?: ((reason: any) => never | PromiseLike<never>) | null | undefined,\n\t): Promise<number> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<number> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,KAAK,WAA4B;AAKnC,MAAM,0BAEH,IAAmD;AAAA,EAsB5D,YACU,QAKR;AACD,UAAM,kBAAkB,mBAAmB,OAAO,QAAQ,OAAO,OAAO,EAAE,WAAW;AAN5E;AAQT,SAAK,QAAQ,MAAM;AAEnB,SAAK,UAAU,OAAO;AAEtB,SAAK,MAAM,kBAAkB;AAAA,MAC5B,OAAO;AAAA,MACP,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EAtCQ;AAAA,EAER,QAA0B,UAAU,IAAI;AAAA,EACxC,CAAC,OAAO,WAAW,IAAI;AAAA,EAEf;AAAA,EAER,OAAe,mBACd,QACA,SACc;AACd,WAAO,4BAAoC,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;AAAA,EAC7F;AAAA,EAEA,OAAe,WACd,QACA,SACc;AACd,WAAO,oCAA4C,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;AAAA,EACrG;AAAA,EAqBA,KACC,aACA,YAC+B;AAC/B,WAAO,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,GAAG,CAAC,EACjD;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAEA,MACC,YACkB;AAClB,WAAO,KAAK,KAAK,QAAW,UAAU;AAAA,EACvC;AAAA,EAEA,QAAQ,WAA8D;AACrE,WAAO,KAAK;AAAA,MACX,CAAC,UAAU;AACV,oBAAY;AACZ,eAAO;AAAA,MACR;AAAA,MACA,CAAC,WAAW;AACX,oBAAY;AACZ,cAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;", "names": []}
{"version": 3, "sources": ["../../src/vercel-postgres/driver.ts"], "sourcesContent": ["import { sql } from '@vercel/postgres';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/index.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { type VercelPgClient, type VercelPgQueryResultHKT, VercelPgSession } from './session.ts';\n\nexport interface VercelPgDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class VercelPgDriver {\n\tstatic readonly [entityKind]: string = 'VercelPgDriver';\n\n\tconstructor(\n\t\tprivate client: VercelPgClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: VercelPgDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): VercelPgSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new VercelPgSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t\tcache: this.options.cache,\n\t\t});\n\t}\n}\n\nexport class VercelPgDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<VercelPgQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'VercelPgDatabase';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: VercelPgClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): VercelPgDatabase<TSchema> & {\n\t$client: VercelPgClient;\n} {\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new VercelPgDriver(client, dialect, { logger, cache: config.cache });\n\tconst session = driver.createSession(schema);\n\tconst db = new VercelPgDatabase(dialect, session, schema as any) as VercelPgDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends VercelPgClient = typeof sql,\n>(\n\t...params: [] | [\n\t\tTClient,\n\t] | [\n\t\tTClient,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tclient?: TClient;\n\t\t\t})\n\t\t),\n\t]\n): VercelPgDatabase<TSchema> & {\n\t$client: VercelPgClient extends TClient ? typeof sql : TClient;\n} {\n\tif (isConfig(params[0])) {\n\t\tconst { client, ...drizzleConfig } = params[0] as ({ client?: TClient } & DrizzleConfig<TSchema>);\n\t\treturn construct(client ?? sql, drizzleConfig) as any;\n\t}\n\n\treturn construct((params[0] ?? sql) as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): VercelPgDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAoB;AAEpB,oBAA2B;AAE3B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAC1B,uBAKO;AACP,mBAA6C;AAC7C,qBAAkF;AAO3E,MAAM,eAAe;AAAA,EAG3B,YACS,QACA,SACA,UAAiC,CAAC,GACzC;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,wBAAU,IAAY;AAAA,EASvC,cACC,QACmE;AACnE,WAAO,IAAI,+BAAgB,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAC7D,QAAQ,KAAK,QAAQ;AAAA,MACrB,OAAO,KAAK,QAAQ;AAAA,IACrB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,yBAEH,qBAA4C;AAAA,EACrD,QAA0B,wBAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,yBAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,eAAe,QAAQ,SAAS,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAClF,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,QAAM,KAAK,IAAI,iBAAiB,SAAS,SAAS,MAAa;AAC/D,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAEO,SAAS,WAIZ,QAeF;AACD,UAAI,uBAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAC7C,WAAO,UAAU,UAAU,qBAAK,aAAa;AAAA,EAC9C;AAEA,SAAO,UAAW,OAAO,CAAC,KAAK,qBAAiB,OAAO,CAAC,CAAuC;AAChG;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}
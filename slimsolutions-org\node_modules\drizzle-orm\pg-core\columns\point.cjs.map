{"version": 3, "sources": ["../../../src/pg-core/columns/point.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgPointTupleBuilderInitial<TName extends string> = PgPointTupleBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgPointTuple';\n\tdata: [number, number];\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgPointTupleBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgPointTuple'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgPointTupleBuilder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'array', 'PgPointTuple');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgPointTuple<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgPointTuple<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgPointTuple<T extends ColumnBaseConfig<'array', 'PgPointTuple'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgPointTuple';\n\n\tgetSQLType(): string {\n\t\treturn 'point';\n\t}\n\n\toverride mapFromDriverValue(value: string | { x: number; y: number }): [number, number] {\n\t\tif (typeof value === 'string') {\n\t\t\tconst [x, y] = value.slice(1, -1).split(',');\n\t\t\treturn [Number.parseFloat(x!), Number.parseFloat(y!)];\n\t\t}\n\t\treturn [value.x, value.y];\n\t}\n\n\toverride mapToDriverValue(value: [number, number]): string {\n\t\treturn `(${value[0]},${value[1]})`;\n\t}\n}\n\nexport type PgPointObjectBuilderInitial<TName extends string> = PgPointObjectBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgPointObject';\n\tdata: { x: number; y: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgPointObjectBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgPointObject'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgPointObjectBuilder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'json', 'PgPointObject');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgPointObject<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgPointObject<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgPointObject<T extends ColumnBaseConfig<'json', 'PgPointObject'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgPointObject';\n\n\tgetSQLType(): string {\n\t\treturn 'point';\n\t}\n\n\toverride mapFromDriverValue(value: string | { x: number; y: number }): { x: number; y: number } {\n\t\tif (typeof value === 'string') {\n\t\t\tconst [x, y] = value.slice(1, -1).split(',');\n\t\t\treturn { x: Number.parseFloat(x!), y: Number.parseFloat(y!) };\n\t\t}\n\t\treturn value;\n\t}\n\n\toverride mapToDriverValue(value: { x: number; y: number }): string {\n\t\treturn `(${value.x},${value.y})`;\n\t}\n}\n\nexport interface PgPointConfig<T extends 'tuple' | 'xy' = 'tuple' | 'xy'> {\n\tmode?: T;\n}\n\nexport function point(): PgPointTupleBuilderInitial<''>;\nexport function point<TMode extends PgPointConfig['mode'] & {}>(\n\tconfig?: PgPointConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgPointObjectBuilderInitial<''>\n\t: PgPointTupleBuilderInitial<''>;\nexport function point<TName extends string, TMode extends PgPointConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgPointConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgPointObjectBuilderInitial<TName>\n\t: PgPointTupleBuilderInitial<TName>;\nexport function point(a?: string | PgPointConfig, b?: PgPointConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgPointConfig>(a, b);\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgPointTupleBuilder(name);\n\t}\n\treturn new PgPointObjectBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,mBAAmD;AACnD,oBAA0C;AAWnC,MAAM,4BACJ,8BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc;AACzB,UAAM,MAAM,SAAS,cAAc;AAAA,EACpC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBAA0E,uBAAY;AAAA,EAClG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAA4D;AACvF,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC3C,aAAO,CAAC,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,CAAC;AAAA,IACrD;AACA,WAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACzB;AAAA,EAES,iBAAiB,OAAiC;AAC1D,WAAO,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EAChC;AACD;AAWO,MAAM,6BACJ,8BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc;AACzB,UAAM,MAAM,QAAQ,eAAe;AAAA,EACpC;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA2E,uBAAY;AAAA,EACnG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAoE;AAC/F,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC3C,aAAO,EAAE,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,EAAE;AAAA,IAC7D;AACA,WAAO;AAAA,EACR;AAAA,EAES,iBAAiB,OAAyC;AAClE,WAAO,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EAC9B;AACD;AAgBO,SAAS,MAAM,GAA4B,GAAmB;AACpE,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAsC,GAAG,CAAC;AACnE,MAAI,CAAC,QAAQ,QAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,oBAAoB,IAAI;AAAA,EACpC;AACA,SAAO,IAAI,qBAAqB,IAAI;AACrC;", "names": []}
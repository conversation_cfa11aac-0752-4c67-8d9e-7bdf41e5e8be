{"version": 3, "sources": ["../../src/pg-core/foreign-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: PgColumn[];\n\treadonly foreignTable: PgTable;\n\treadonly foreignColumns: PgColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgForeignKeyBuilder';\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined = 'no action';\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined = 'no action';\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: PgColumn[];\n\t\t\tforeignColumns: PgColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as PgTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport type AnyForeignKeyBuilder = ForeignKeyBuilder;\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'PgForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: PgTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[TableName],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[TableName],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends PgColumn[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TTableName }> };\n\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnyPgColumn<{ tableName: TTableName }>, ...AnyPgColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tconst { name, columns, foreignColumns } = config;\n\t\treturn {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tforeignColumns,\n\t\t};\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAanB,MAAM,kBAAkB;AAAA,EAC9B,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGA,YAA4C;AAAA;AAAA,EAG5C,YAA4C;AAAA,EAE5C,YACC,QAKA,SAIC;AACD,SAAK,YAAY,MAAM;AACtB,YAAM,EAAE,MAAM,SAAS,eAAe,IAAI,OAAO;AACjD,aAAO,EAAE,MAAM,SAAS,cAAc,eAAe,CAAC,EAAG,OAAkB,eAAe;AAAA,IAC3F;AACA,QAAI,SAAS;AACZ,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAAA,EACD;AAAA,EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY,WAAW,SAAY,cAAc;AACtD,WAAO;AAAA,EACR;AAAA,EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY,WAAW,SAAY,cAAc;AACtD,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAA4B;AACjC,WAAO,IAAI,WAAW,OAAO,IAAI;AAAA,EAClC;AACD;AAIO,MAAM,WAAW;AAAA,EAOvB,YAAqB,OAAgB,SAA4B;AAA5C;AACpB,SAAK,YAAY,QAAQ;AACzB,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,QAAQ;AAAA,EACzB;AAAA,EAVA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA;AAAA,EAQT,UAAkB;AACjB,UAAM,EAAE,MAAM,SAAS,eAAe,IAAI,KAAK,UAAU;AACzD,UAAM,cAAc,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI;AACvD,UAAM,qBAAqB,eAAe,IAAI,CAAC,WAAW,OAAO,IAAI;AACrE,UAAM,SAAS;AAAA,MACd,KAAK,MAAM,SAAS;AAAA,MACpB,GAAG;AAAA,MACH,eAAe,CAAC,EAAG,MAAM,SAAS;AAAA,MAClC,GAAG;AAAA,IACJ;AACA,WAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,CAAC;AAAA,EACnC;AACD;AAOO,SAAS,WAKf,QAKoB;AACpB,WAAS,eAAe;AACvB,UAAM,EAAE,MAAM,SAAS,eAAe,IAAI;AAC1C,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,kBAAkB,YAAY;AAC1C;", "names": []}
{"version": 3, "sources": ["../../../src/pg-core/query-builders/select.types.ts"], "sourcesContent": ["import type {\n\tSelectedFields as SelectedFieldsBase,\n\tSelectedFieldsFlat as SelectedFieldsFlatBase,\n\tSelectedFieldsOrdered as SelectedFieldsOrderedBase,\n} from '~/operations.ts';\nimport type { PgColumn } from '~/pg-core/columns/index.ts';\nimport type { PgTable, PgTableWithColumns } from '~/pg-core/table.ts';\nimport type { PgViewBase } from '~/pg-core/view-base.ts';\nimport type { PgViewWithSelection } from '~/pg-core/view.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tJoinNullability,\n\tJoinType,\n\tMapColumnsToTableAlias,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection, Placeholder, SQL, SQLWrapper, View } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { Table, UpdateTableConfig } from '~/table.ts';\nimport type { Assume, DrizzleTypeError, Equal, ValidateShape, ValueOrArray } from '~/utils.ts';\nimport type { PgPreparedQuery, PreparedQueryConfig } from '../session.ts';\nimport type { PgSelectBase, PgSelectQueryBuilderBase } from './select.ts';\n\nexport interface PgSelectJoinConfig {\n\ton: SQL | undefined;\n\ttable: PgTable | Subquery | PgViewBase | SQL;\n\talias: string | undefined;\n\tjoinType: JoinType;\n\tlateral?: boolean;\n}\n\nexport type BuildAliasTable<TTable extends PgTable | View, TAlias extends string> = TTable extends Table\n\t? PgTableWithColumns<\n\t\tUpdateTableConfig<TTable['_']['config'], {\n\t\t\tname: TAlias;\n\t\t\tcolumns: MapColumnsToTableAlias<TTable['_']['columns'], TAlias, 'pg'>;\n\t\t}>\n\t>\n\t: TTable extends View ? PgViewWithSelection<\n\t\t\tTAlias,\n\t\t\tTTable['_']['existing'],\n\t\t\tMapColumnsToTableAlias<TTable['_']['selectedFields'], TAlias, 'pg'>\n\t\t>\n\t: never;\n\nexport interface PgSelectConfig {\n\twithList?: Subquery[];\n\t// Either fields or fieldsFlat must be defined\n\tfields: Record<string, unknown>;\n\tfieldsFlat?: SelectedFieldsOrdered;\n\twhere?: SQL;\n\thaving?: SQL;\n\ttable: PgTable | Subquery | PgViewBase | SQL;\n\tlimit?: number | Placeholder;\n\toffset?: number | Placeholder;\n\tjoins?: PgSelectJoinConfig[];\n\torderBy?: (PgColumn | SQL | SQL.Aliased)[];\n\tgroupBy?: (PgColumn | SQL | SQL.Aliased)[];\n\tlockingClause?: {\n\t\tstrength: LockStrength;\n\t\tconfig: LockConfig;\n\t};\n\tdistinct?: boolean | {\n\t\ton: (PgColumn | SQLWrapper)[];\n\t};\n\tsetOperators: {\n\t\trightSelect: TypedQueryBuilder<any, any>;\n\t\ttype: SetOperator;\n\t\tisAll: boolean;\n\t\torderBy?: (PgColumn | SQL | SQL.Aliased)[];\n\t\tlimit?: number | Placeholder;\n\t\toffset?: number | Placeholder;\n\t}[];\n}\n\nexport type TableLikeHasEmptySelection<T extends PgTable | Subquery | PgViewBase | SQL> = T extends Subquery\n\t? Equal<T['_']['selectedFields'], {}> extends true ? true : false\n\t: false;\n\nexport type PgSelectJoin<\n\tT extends AnyPgSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n> = T extends any ? PgSelectWithout<\n\t\tPgSelectKind<\n\t\t\tT['_']['hkt'],\n\t\t\tT['_']['tableName'],\n\t\t\tAppendToResult<\n\t\t\t\tT['_']['tableName'],\n\t\t\t\tT['_']['selection'],\n\t\t\t\tTJoinedName,\n\t\t\t\tTJoinedTable extends Table ? TJoinedTable['_']['columns']\n\t\t\t\t\t: TJoinedTable extends Subquery | View ? Assume<TJoinedTable['_']['selectedFields'], SelectedFields>\n\t\t\t\t\t: never,\n\t\t\t\tT['_']['selectMode']\n\t\t\t>,\n\t\t\tT['_']['selectMode'] extends 'partial' ? T['_']['selectMode'] : 'multiple',\n\t\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], TJoinedName, TJoinType>,\n\t\t\tT['_']['dynamic'],\n\t\t\tT['_']['excludedMethods']\n\t\t>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>\n\t: never;\n\nexport type PgSelectJoinFn<\n\tT extends AnyPgSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTIsLateral extends boolean,\n> = <\n\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : PgTable | Subquery | PgViewBase | SQL),\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n>(\n\ttable: TableLikeHasEmptySelection<TJoinedTable> extends true ? DrizzleTypeError<\n\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t>\n\t\t: TJoinedTable,\n\ton: ((aliases: T['_']['selection']) => SQL | undefined) | SQL | undefined,\n) => PgSelectJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>;\n\nexport type PgSelectCrossJoinFn<\n\tT extends AnyPgSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTIsLateral extends boolean,\n> = <\n\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : PgTable | Subquery | PgViewBase | SQL),\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n>(\n\ttable: TableLikeHasEmptySelection<TJoinedTable> extends true ? DrizzleTypeError<\n\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t>\n\t\t: TJoinedTable,\n) => PgSelectJoin<T, TDynamic, 'cross', TJoinedTable, TJoinedName>;\n\nexport type SelectedFieldsFlat = SelectedFieldsFlatBase<PgColumn>;\n\nexport type SelectedFields = SelectedFieldsBase<PgColumn, PgTable>;\n\nexport type SelectedFieldsOrdered = SelectedFieldsOrderedBase<PgColumn>;\n\nexport type LockStrength = 'update' | 'no key update' | 'share' | 'key share';\n\nexport type LockConfig =\n\t& {\n\t\tof?: ValueOrArray<PgTable>;\n\t}\n\t& ({\n\t\tnoWait: true;\n\t\tskipLocked?: undefined;\n\t} | {\n\t\tnoWait?: undefined;\n\t\tskipLocked: true;\n\t} | {\n\t\tnoWait?: undefined;\n\t\tskipLocked?: undefined;\n\t});\n\nexport interface PgSelectHKTBase {\n\ttableName: string | undefined;\n\tselection: unknown;\n\tselectMode: SelectMode;\n\tnullabilityMap: unknown;\n\tdynamic: boolean;\n\texcludedMethods: string;\n\tresult: unknown;\n\tselectedFields: unknown;\n\t_type: unknown;\n}\n\nexport type PgSelectKind<\n\tT extends PgSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability>,\n\tTDynamic extends boolean,\n\tTExcludedMethods extends string,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> = (T & {\n\ttableName: TTableName;\n\tselection: TSelection;\n\tselectMode: TSelectMode;\n\tnullabilityMap: TNullabilityMap;\n\tdynamic: TDynamic;\n\texcludedMethods: TExcludedMethods;\n\tresult: TResult;\n\tselectedFields: TSelectedFields;\n})['_type'];\n\nexport interface PgSelectQueryBuilderHKT extends PgSelectHKTBase {\n\t_type: PgSelectQueryBuilderBase<\n\t\tPgSelectQueryBuilderHKT,\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport interface PgSelectHKT extends PgSelectHKTBase {\n\t_type: PgSelectBase<\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport type CreatePgSelectFromBuilderMode<\n\tTBuilderMode extends 'db' | 'qb',\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n> = TBuilderMode extends 'db' ? PgSelectBase<TTableName, TSelection, TSelectMode>\n\t: PgSelectQueryBuilderBase<PgSelectQueryBuilderHKT, TTableName, TSelection, TSelectMode>;\n\nexport type PgSetOperatorExcludedMethods =\n\t| 'leftJoin'\n\t| 'rightJoin'\n\t| 'innerJoin'\n\t| 'fullJoin'\n\t| 'where'\n\t| 'having'\n\t| 'groupBy'\n\t| 'for';\n\nexport type PgSelectWithout<\n\tT extends AnyPgSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n\tTResetExcluded extends boolean = false,\n> = TDynamic extends true ? T : Omit<\n\tPgSelectKind<\n\t\tT['_']['hkt'],\n\t\tT['_']['tableName'],\n\t\tT['_']['selection'],\n\t\tT['_']['selectMode'],\n\t\tT['_']['nullabilityMap'],\n\t\tTDynamic,\n\t\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K,\n\t\tT['_']['result'],\n\t\tT['_']['selectedFields']\n\t>,\n\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K\n>;\n\nexport type PgSelectPrepare<T extends AnyPgSelect> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['result'];\n\t}\n>;\n\nexport type PgSelectDynamic<T extends AnyPgSelectQueryBuilder> = PgSelectKind<\n\tT['_']['hkt'],\n\tT['_']['tableName'],\n\tT['_']['selection'],\n\tT['_']['selectMode'],\n\tT['_']['nullabilityMap'],\n\ttrue,\n\tnever,\n\tT['_']['result'],\n\tT['_']['selectedFields']\n>;\n\nexport type PgSelectQueryBuilder<\n\tTHKT extends PgSelectHKTBase = PgSelectQueryBuilderHKT,\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n\tTResult extends any[] = unknown[],\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = PgSelectQueryBuilderBase<\n\tTHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tnever,\n\tTResult,\n\tTSelectedFields\n>;\n\nexport type AnyPgSelectQueryBuilder = PgSelectQueryBuilderBase<any, any, any, any, any, any, any, any, any>;\n\nexport type AnyPgSetOperatorInterface = PgSetOperatorInterface<any, any, any, any, any, any, any, any>;\n\nexport interface PgSetOperatorInterface<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> {\n\t_: {\n\t\treadonly hkt: PgSelectHKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n}\n\nexport type PgSetOperatorWithResult<TResult extends any[]> = PgSetOperatorInterface<\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tTResult,\n\tany\n>;\n\nexport type PgSelect<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = PgSelectBase<TTableName, TSelection, TSelectMode, TNullabilityMap, true, never>;\n\nexport type AnyPgSelect = PgSelectBase<any, any, any, any, any, any, any, any>;\n\nexport type PgSetOperator<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = PgSelectBase<\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tPgSetOperatorExcludedMethods\n>;\n\nexport type SetOperatorRightSelect<\n\tTValue extends PgSetOperatorWithResult<TResult>,\n\tTResult extends any[],\n> = TValue extends PgSetOperatorInterface<any, any, any, any, any, any, infer TValueResult, any> ? ValidateShape<\n\t\tTValueResult[number],\n\t\tTResult[number],\n\t\tTypedQueryBuilder<any, TValueResult>\n\t>\n\t: TValue;\n\nexport type SetOperatorRestSelect<\n\tTValue extends readonly PgSetOperatorWithResult<TResult>[],\n\tTResult extends any[],\n> = TValue extends [infer First, ...infer Rest]\n\t? First extends PgSetOperatorInterface<any, any, any, any, any, any, infer TValueResult, any>\n\t\t? Rest extends AnyPgSetOperatorInterface[] ? [\n\t\t\t\tValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>>,\n\t\t\t\t...SetOperatorRestSelect<Rest, TResult>,\n\t\t\t]\n\t\t: ValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>[]>\n\t: never\n\t: TValue;\n\nexport type PgCreateSetOperatorFn = <\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTValue extends PgSetOperatorWithResult<TResult>,\n\tTRest extends PgSetOperatorWithResult<TResult>[],\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n>(\n\tleftSelect: PgSetOperatorInterface<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\trightSelect: SetOperatorRightSelect<TValue, TResult>,\n\t...restSelects: SetOperatorRestSelect<TRest, TResult>\n) => PgSelectWithout<\n\tPgSelectBase<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tfalse,\n\tPgSetOperatorExcludedMethods,\n\ttrue\n>;\n\nexport type GetPgSetOperators = {\n\tunion: PgCreateSetOperatorFn;\n\tintersect: PgCreateSetOperatorFn;\n\texcept: PgCreateSetOperatorFn;\n\tunionAll: PgCreateSetOperatorFn;\n\tintersectAll: PgCreateSetOperatorFn;\n\texceptAll: PgCreateSetOperatorFn;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}
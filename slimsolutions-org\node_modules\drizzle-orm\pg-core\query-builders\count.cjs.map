{"version": 3, "sources": ["../../../src/pg-core/query-builders/count.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { SQL, sql, type S<PERSON>Wrapper } from '~/sql/sql.ts';\nimport type { NeonAuthToken } from '~/utils.ts';\nimport type { PgSession } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class PgCountBuilder<\n\tTSession extends PgSession<any, any, any>,\n> extends SQL<number> implements Promise<number>, SQLWrapper {\n\tprivate sql: SQL<number>;\n\tprivate token?: NeonAuthToken;\n\n\tstatic override readonly [entityKind] = 'PgCountBuilder';\n\t[Symbol.toStringTag] = 'PgCountBuilder';\n\n\tprivate session: TSession;\n\n\tprivate static buildEmbeddedCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`(select count(*) from ${source}${sql.raw(' where ').if(filters)}${filters})`;\n\t}\n\n\tprivate static buildCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`select count(*) as count from ${source}${sql.raw(' where ').if(filters)}${filters};`;\n\t}\n\n\tconstructor(\n\t\treadonly params: {\n\t\t\tsource: PgTable | SQL | SQLWrapper;\n\t\t\tfilters?: SQL<unknown>;\n\t\t\tsession: TSession;\n\t\t},\n\t) {\n\t\tsuper(PgCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n\n\t\tthis.mapWith(Number);\n\n\t\tthis.session = params.session;\n\n\t\tthis.sql = PgCountBuilder.buildCount(\n\t\t\tparams.source,\n\t\t\tparams.filters,\n\t\t);\n\t}\n\n\t/** @intrnal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.token = token;\n\t\treturn this;\n\t}\n\n\tthen<TResult1 = number, TResult2 = never>(\n\t\tonfulfilled?: ((value: number) => TResult1 | PromiseLike<TResult1>) | null | undefined,\n\t\tonrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn Promise.resolve(this.session.count(this.sql, this.token))\n\t\t\t.then(\n\t\t\t\tonfulfilled,\n\t\t\t\tonrejected,\n\t\t\t);\n\t}\n\n\tcatch(\n\t\tonRejected?: ((reason: any) => any) | null | undefined,\n\t): Promise<number> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<number> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,iBAA0C;AAKnC,MAAM,uBAEH,eAAmD;AAAA,EAuB5D,YACU,QAKR;AACD,UAAM,eAAe,mBAAmB,OAAO,QAAQ,OAAO,OAAO,EAAE,WAAW;AANzE;AAQT,SAAK,QAAQ,MAAM;AAEnB,SAAK,UAAU,OAAO;AAEtB,SAAK,MAAM,eAAe;AAAA,MACzB,OAAO;AAAA,MACP,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EAvCQ;AAAA,EACA;AAAA,EAER,QAA0B,wBAAU,IAAI;AAAA,EACxC,CAAC,OAAO,WAAW,IAAI;AAAA,EAEf;AAAA,EAER,OAAe,mBACd,QACA,SACc;AACd,WAAO,uCAAoC,MAAM,GAAG,eAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;AAAA,EAC7F;AAAA,EAEA,OAAe,WACd,QACA,SACc;AACd,WAAO,+CAA4C,MAAM,GAAG,eAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;AAAA,EACrG;AAAA;AAAA,EAsBA,SAAS,OAAuB;AAC/B,SAAK,QAAQ;AACb,WAAO;AAAA,EACR;AAAA,EAEA,KACC,aACA,YAC+B;AAC/B,WAAO,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,CAAC,EAC7D;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAEA,MACC,YACkB;AAClB,WAAO,KAAK,KAAK,QAAW,UAAU;AAAA,EACvC;AAAA,EAEA,QAAQ,WAA8D;AACrE,WAAO,KAAK;AAAA,MACX,CAAC,UAAU;AACV,oBAAY;AACZ,eAAO;AAAA,MACR;AAAA,MACA,CAAC,WAAW;AACX,oBAAY;AACZ,cAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD;AACD;", "names": []}
{"version": 3, "sources": ["../../src/neon-http/driver.ts"], "sourcesContent": ["import type { HTTPQueryOptions, HTTPTransactionOptions, NeonQueryFunction } from '@neondatabase/serverless';\nimport { neon, types } from '@neondatabase/serverless';\nimport type { BatchItem, BatchResponse } from '~/batch.ts';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport { createTableRelationsHelpers, extractTablesRelationalConfig } from '~/relations.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { type NeonHttpClient, type NeonHttpQueryResultHKT, NeonHttpSession } from './session.ts';\n\nexport interface NeonDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class NeonHttpDriver {\n\tstatic readonly [entityKind]: string = 'NeonHttpDriver';\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: NeonDriverOptions = {},\n\t) {\n\t\tthis.initMappers();\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): NeonHttpSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new NeonHttpSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t\tcache: this.options.cache,\n\t\t});\n\t}\n\n\tinitMappers() {\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMP, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.DATE, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.INTERVAL, (val) => val);\n\t\ttypes.setTypeParser(1231, (val) => val);\n\t\ttypes.setTypeParser(1115, (val) => val);\n\t\ttypes.setTypeParser(1185, (val) => val);\n\t\ttypes.setTypeParser(1187, (val) => val);\n\t\ttypes.setTypeParser(1182, (val) => val);\n\t}\n}\n\nfunction wrap<T extends object>(\n\ttarget: T,\n\ttoken: Exclude<HTTPQueryOptions<true, true>['authToken'], undefined>,\n\tcb: (target: any, p: string | symbol, res: any) => any,\n\tdeep?: boolean,\n) {\n\treturn new Proxy(target, {\n\t\tget(target, p) {\n\t\t\tconst element = target[p as keyof typeof p];\n\t\t\tif (typeof element !== 'function' && (typeof element !== 'object' || element === null)) return element;\n\n\t\t\tif (deep) return wrap(element, token, cb);\n\t\t\tif (p === 'query') return wrap(element, token, cb, true);\n\n\t\t\treturn new Proxy(element as any, {\n\t\t\t\tapply(target, thisArg, argArray) {\n\t\t\t\t\tconst res = target.call(thisArg, ...argArray);\n\t\t\t\t\tif (typeof res === 'object' && res !== null && 'setToken' in res && typeof res.setToken === 'function') {\n\t\t\t\t\t\tres.setToken(token);\n\t\t\t\t\t}\n\t\t\t\t\treturn cb(target, p, res);\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t});\n}\n\nexport class NeonHttpDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<NeonHttpQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonHttpDatabase';\n\n\t$withAuth(\n\t\ttoken: Exclude<HTTPQueryOptions<true, true>['authToken'], undefined>,\n\t): Omit<\n\t\tthis,\n\t\tExclude<\n\t\t\tkeyof this,\n\t\t\t| '$count'\n\t\t\t| 'delete'\n\t\t\t| 'select'\n\t\t\t| 'selectDistinct'\n\t\t\t| 'selectDistinctOn'\n\t\t\t| 'update'\n\t\t\t| 'insert'\n\t\t\t| 'with'\n\t\t\t| 'query'\n\t\t\t| 'execute'\n\t\t\t| 'refreshMaterializedView'\n\t\t>\n\t> {\n\t\tthis.authToken = token;\n\n\t\treturn wrap(this, token, (target, p, res) => {\n\t\t\tif (p === 'with') {\n\t\t\t\treturn wrap(res, token, (_, __, res) => res);\n\t\t\t}\n\t\t\treturn res;\n\t\t});\n\t}\n\n\t/** @internal */\n\tdeclare readonly session: NeonHttpSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'pg'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends NeonQueryFunction<any, any> = NeonQueryFunction<any, any>,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): NeonHttpDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new NeonHttpDriver(client, dialect, { logger, cache: config.cache });\n\tconst session = driver.createSession(schema);\n\n\tconst db = new NeonHttpDatabase(\n\t\tdialect,\n\t\tsession,\n\t\tschema as RelationalSchemaConfig<ExtractTablesWithRelations<TSchema>> | undefined,\n\t);\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends NeonQueryFunction<any, any> = NeonQueryFunction<false, false>,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | ({ connectionString: string } & HTTPTransactionOptions<boolean, boolean>);\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): NeonHttpDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = neon(params[0] as string);\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& {\n\t\t\t\tconnection?:\n\t\t\t\t\t| ({\n\t\t\t\t\t\tconnectionString: string;\n\t\t\t\t\t} & HTTPTransactionOptions<boolean, boolean>)\n\t\t\t\t\t| string;\n\t\t\t\tclient?: TClient;\n\t\t\t}\n\t\t\t& DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig);\n\n\t\tif (typeof connection === 'object') {\n\t\t\tconst { connectionString, ...options } = connection;\n\n\t\t\tconst instance = neon(connectionString, options);\n\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = neon(connection!);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): NeonHttpDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AACA,SAAS,MAAM,aAAa;AAG5B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B,SAAS,6BAA6B,qCAAqC;AAE3E,SAA6B,gBAAgB;AAC7C,SAA2D,uBAAuB;AAO3E,MAAM,eAAe;AAAA,EAG3B,YACS,QACA,SACA,UAA6B,CAAC,GACrC;AAHO;AACA;AACA;AAER,SAAK,YAAY;AAAA,EAClB;AAAA,EARA,QAAiB,UAAU,IAAY;AAAA,EAUvC,cACC,QACmE;AACnE,WAAO,IAAI,gBAAgB,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAC7D,QAAQ,KAAK,QAAQ;AAAA,MACrB,OAAO,KAAK,QAAQ;AAAA,IACrB,CAAC;AAAA,EACF;AAAA,EAEA,cAAc;AACb,UAAM,cAAc,MAAM,SAAS,aAAa,CAAC,QAAQ,GAAG;AAC5D,UAAM,cAAc,MAAM,SAAS,WAAW,CAAC,QAAQ,GAAG;AAC1D,UAAM,cAAc,MAAM,SAAS,MAAM,CAAC,QAAQ,GAAG;AACrD,UAAM,cAAc,MAAM,SAAS,UAAU,CAAC,QAAQ,GAAG;AACzD,UAAM,cAAc,MAAM,CAAC,QAAQ,GAAG;AACtC,UAAM,cAAc,MAAM,CAAC,QAAQ,GAAG;AACtC,UAAM,cAAc,MAAM,CAAC,QAAQ,GAAG;AACtC,UAAM,cAAc,MAAM,CAAC,QAAQ,GAAG;AACtC,UAAM,cAAc,MAAM,CAAC,QAAQ,GAAG;AAAA,EACvC;AACD;AAEA,SAAS,KACR,QACA,OACA,IACA,MACC;AACD,SAAO,IAAI,MAAM,QAAQ;AAAA,IACxB,IAAIA,SAAQ,GAAG;AACd,YAAM,UAAUA,QAAO,CAAmB;AAC1C,UAAI,OAAO,YAAY,eAAe,OAAO,YAAY,YAAY,YAAY,MAAO,QAAO;AAE/F,UAAI,KAAM,QAAO,KAAK,SAAS,OAAO,EAAE;AACxC,UAAI,MAAM,QAAS,QAAO,KAAK,SAAS,OAAO,IAAI,IAAI;AAEvD,aAAO,IAAI,MAAM,SAAgB;AAAA,QAChC,MAAMA,SAAQ,SAAS,UAAU;AAChC,gBAAM,MAAMA,QAAO,KAAK,SAAS,GAAG,QAAQ;AAC5C,cAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,cAAc,OAAO,OAAO,IAAI,aAAa,YAAY;AACvG,gBAAI,SAAS,KAAK;AAAA,UACnB;AACA,iBAAO,GAAGA,SAAQ,GAAG,GAAG;AAAA,QACzB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD,CAAC;AACF;AAEO,MAAM,yBAEH,WAA4C;AAAA,EACrD,QAA0B,UAAU,IAAY;AAAA,EAEhD,UACC,OAiBC;AACD,SAAK,YAAY;AAEjB,WAAO,KAAK,MAAM,OAAO,CAAC,QAAQ,GAAG,QAAQ;AAC5C,UAAI,MAAM,QAAQ;AACjB,eAAO,KAAK,KAAK,OAAO,CAAC,GAAG,IAAIC,SAAQA,IAAG;AAAA,MAC5C;AACA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EAKA,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAEA,SAAS,UAIR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,UAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,eAAe,QAAQ,SAAS,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAClF,QAAM,UAAU,OAAO,cAAc,MAAM;AAE3C,QAAM,KAAK,IAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAEO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,KAAK,OAAO,CAAC,CAAW;AACzC,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAWzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAO,eAAe,UAAU;AACnC,YAAM,EAAE,kBAAkB,GAAG,QAAQ,IAAI;AAEzC,YAAMC,YAAW,KAAK,kBAAkB,OAAO;AAE/C,aAAO,UAAUA,WAAU,aAAa;AAAA,IACzC;AAEA,UAAM,WAAW,KAAK,UAAW;AAEjC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["target", "res", "instance", "drizzle"]}
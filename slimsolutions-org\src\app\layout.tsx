import type { Metada<PERSON> } from "next";
import { Open_Sans, Playfair_Display, Inter } from "next/font/google";
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import "./globals.css";

// Body font - Open Sans
const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  weight: ["400", "600"],
  display: 'swap',
});

// Heading font - Playfair Display
const playfairDisplay = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
  display: 'swap',
});

// UI font - Inter
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500", "600"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "SlimSolutions - Your Guide to Healthy Weight Loss",
  description: "Discover effective weight loss strategies, healthy recipes, and lifestyle tips for sustainable weight management and a healthier lifestyle.",
  keywords: "weight loss, healthy lifestyle, diet, fitness, wellness, nutrition, fat burning, metabolism",
  authors: [{ name: "SlimSolutions" }],
  creator: "SlimSolutions",
  publisher: "SlimSolutions",
  metadataBase: new URL('https://slimsolutions.org'),
  openGraph: {
    title: "SlimSolutions - Your Guide to Healthy Weight Loss",
    description: "Discover effective weight loss strategies, healthy recipes, and lifestyle tips for sustainable weight management.",
    url: 'https://slimsolutions.org',
    siteName: 'SlimSolutions',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "SlimSolutions - Your Guide to Healthy Weight Loss",
    description: "Discover effective weight loss strategies, healthy recipes, and lifestyle tips for sustainable weight management.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${openSans.variable} ${playfairDisplay.variable} ${inter.variable} font-sans antialiased`}
      >
        {children}
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}

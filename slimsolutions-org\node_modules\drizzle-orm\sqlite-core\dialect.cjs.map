{"version": 3, "sources": ["../../src/sqlite-core/dialect.ts"], "sourcesContent": ["import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { CasingCache } from '~/casing.ts';\nimport type { AnyColumn } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleError } from '~/errors.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { Name, Placeholder } from '~/sql/index.ts';\nimport { and, eq } from '~/sql/index.ts';\nimport { Param, type QueryWithTypings, SQL, sql, type SQLChunk } from '~/sql/sql.ts';\nimport { SQLiteColumn } from '~/sqlite-core/columns/index.ts';\nimport type {\n\tAnySQLiteSelectQueryBuilder,\n\tSQLiteDeleteConfig,\n\tSQLiteInsertConfig,\n\tSQLiteUpdateConfig,\n} from '~/sqlite-core/query-builders/index.ts';\nimport { SQLiteTable } from '~/sqlite-core/table.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, getTableUniqueName, Table } from '~/table.ts';\nimport { type Casing, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type {\n\tSelectedFieldsOrdered,\n\tSQLiteSelectConfig,\n\tSQLiteSelectJoinConfig,\n} from './query-builders/select.types.ts';\nimport type { SQLiteSession } from './session.ts';\nimport { SQLiteViewBase } from './view-base.ts';\n\nexport interface SQLiteDialectConfig {\n\tcasing?: Casing;\n}\n\nexport abstract class SQLiteDialect {\n\tstatic readonly [entityKind]: string = 'SQLiteDialect';\n\n\t/** @internal */\n\treadonly casing: CasingCache;\n\n\tconstructor(config?: SQLiteDialectConfig) {\n\t\tthis.casing = new CasingCache(config?.casing);\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\"${name}\"`;\n\t}\n\n\tescapeParam(_num: number): string {\n\t\treturn '?';\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList, limit, orderBy }: SQLiteDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${returningSql}${orderBySql}${limitSql}`;\n\t}\n\n\tbuildUpdateSet(table: SQLiteTable, set: UpdateSet): SQL {\n\t\tconst tableColumns = table[Table.Symbol.Columns];\n\n\t\tconst columnNames = Object.keys(tableColumns).filter((colName) =>\n\t\t\tset[colName] !== undefined || tableColumns[colName]?.onUpdateFn !== undefined\n\t\t);\n\n\t\tconst setSize = columnNames.length;\n\t\treturn sql.join(columnNames.flatMap((colName, i) => {\n\t\t\tconst col = tableColumns[colName]!;\n\n\t\t\tconst value = set[colName] ?? sql.param(col.onUpdateFn!(), col);\n\t\t\tconst res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n\n\t\t\tif (i < setSize - 1) {\n\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t}\n\t\t\treturn [res];\n\t\t}));\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList, joins, from, limit, orderBy }: SQLiteUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst fromSql = from && sql.join([sql.raw(' from '), this.buildFromTable(from)]);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\treturn sql`${withSql}update ${table} set ${setSql}${fromSql}${joinsSql}${whereSql}${returningSql}${orderBySql}${limitSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, Column)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(this.casing.getColumnCasing(c));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tconst tableName = field.table[Table.Symbol.Name];\n\t\t\t\t\tif (field.columnType === 'SQLiteNumericBigInt') {\n\t\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\t\tchunk.push(sql`cast(${sql.identifier(this.casing.getColumnCasing(field))} as text)`);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\t\tsql`cast(${sql.identifier(tableName)}.${sql.identifier(this.casing.getColumnCasing(field))} as text)`,\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\t\tchunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tchunk.push(sql`${sql.identifier(tableName)}.${sql.identifier(this.casing.getColumnCasing(field))}`);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tprivate buildJoins(joins: SQLiteSelectJoinConfig[] | undefined): SQL | undefined {\n\t\tif (!joins || joins.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tif (joins) {\n\t\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\t\tif (index === 0) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t\tconst table = joinMeta.table;\n\t\t\t\tconst onSql = joinMeta.on ? sql` on ${joinMeta.on}` : undefined;\n\n\t\t\t\tif (is(table, SQLiteTable)) {\n\t\t\t\t\tconst tableName = table[SQLiteTable.Symbol.Name];\n\t\t\t\t\tconst tableSchema = table[SQLiteTable.Symbol.Schema];\n\t\t\t\t\tconst origTableName = table[SQLiteTable.Symbol.OriginalName];\n\t\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join ${tableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined}${\n\t\t\t\t\t\t\tsql.identifier(origTableName)\n\t\t\t\t\t\t}${alias && sql` ${sql.identifier(alias)}`}${onSql}`,\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join ${table}${onSql}`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tif (index < joins.length - 1) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn sql.join(joinsArray);\n\t}\n\n\tprivate buildLimit(limit: number | Placeholder | undefined): SQL | undefined {\n\t\treturn typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\t}\n\n\tprivate buildOrderBy(orderBy: (SQLiteColumn | SQL | SQL.Aliased)[] | undefined): SQL | undefined {\n\t\tconst orderByList: (SQLiteColumn | SQL | SQL.Aliased)[] = [];\n\n\t\tif (orderBy) {\n\t\t\tfor (const [index, orderByValue] of orderBy.entries()) {\n\t\t\t\torderByList.push(orderByValue);\n\n\t\t\t\tif (index < orderBy.length - 1) {\n\t\t\t\t\torderByList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn orderByList.length > 0 ? sql` order by ${sql.join(orderByList)}` : undefined;\n\t}\n\n\tprivate buildFromTable(\n\t\ttable: SQL | Subquery | SQLiteViewBase | SQLiteTable | undefined,\n\t): SQL | Subquery | SQLiteViewBase | SQLiteTable | undefined {\n\t\tif (is(table, Table) && table[Table.Symbol.IsAlias]) {\n\t\t\treturn sql`${sql`${sql.identifier(table[Table.Symbol.Schema] ?? '')}.`.if(table[Table.Symbol.Schema])}${\n\t\t\t\tsql.identifier(table[Table.Symbol.OriginalName])\n\t\t\t} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t}\n\n\t\treturn table;\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t}: SQLiteSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<SQLiteColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table._.alias\n\t\t\t\t\t\t: is(table, SQLiteViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst distinctSql = distinct ? sql` distinct` : undefined;\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = this.buildFromTable(table);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tconst groupByList: (SQL | AnyColumn | SQL.Aliased)[] = [];\n\t\tif (groupBy) {\n\t\t\tfor (const [index, groupByValue] of groupBy.entries()) {\n\t\t\t\tgroupByList.push(groupByValue);\n\n\t\t\t\tif (index < groupBy.length - 1) {\n\t\t\t\t\tgroupByList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst groupBySql = groupByList.length > 0 ? sql` group by ${sql.join(groupByList)}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: SQLiteSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: SQLiteSelectConfig['setOperators'][number] }): SQL {\n\t\t// SQLite doesn't support parenthesis in set operations\n\t\tconst leftChunk = sql`${leftSelect.getSQL()} `;\n\t\tconst rightChunk = sql`${rightSelect.getSQL()}`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid Sql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const singleOrderBy of orderBy) {\n\t\t\t\tif (is(singleOrderBy, SQLiteColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(singleOrderBy.name));\n\t\t\t\t} else if (is(singleOrderBy, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = singleOrderBy.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, SQLiteColumn)) {\n\t\t\t\t\t\t\tsingleOrderBy.queryChunks[i] = sql.identifier(this.casing.getColumnCasing(chunk));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)}`;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery(\n\t\t{ table, values: valuesOrSelect, onConflict, returning, withList, select }: SQLiteInsertConfig,\n\t): SQL {\n\t\t// const isSingleValue = values.length === 1;\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, SQLiteColumn> = table[Table.Symbol.Columns];\n\n\t\tconst colEntries: [string, SQLiteColumn][] = Object.entries(columns).filter(([_, col]) =>\n\t\t\t!col.shouldDisableInsert()\n\t\t);\n\t\tconst insertOrder = colEntries.map(([, column]) => sql.identifier(this.casing.getColumnCasing(column)));\n\n\t\tif (select) {\n\t\t\tconst select = valuesOrSelect as AnySQLiteSelectQueryBuilder | SQL;\n\n\t\t\tif (is(select, SQL)) {\n\t\t\t\tvaluesSqlList.push(select);\n\t\t\t} else {\n\t\t\t\tvaluesSqlList.push(select.getSQL());\n\t\t\t}\n\t\t} else {\n\t\t\tconst values = valuesOrSelect as Record<string, Param | SQL>[];\n\t\t\tvaluesSqlList.push(sql.raw('values '));\n\n\t\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t\tlet defaultValue;\n\t\t\t\t\t\tif (col.default !== null && col.default !== undefined) {\n\t\t\t\t\t\t\tdefaultValue = is(col.default, SQL) ? col.default : sql.param(col.default, col);\n\t\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\t} else if (col.defaultFn !== undefined) {\n\t\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\t\tdefaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\t} else if (!col.default && col.onUpdateFn !== undefined) {\n\t\t\t\t\t\t\tconst onUpdateFnResult = col.onUpdateFn();\n\t\t\t\t\t\t\tdefaultValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdefaultValue = sql`null`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tvaluesSqlList.push(valueList);\n\t\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst onConflictSql = onConflict?.length\n\t\t\t? sql.join(onConflict)\n\t\t\t: undefined;\n\n\t\t// if (isSingleValue && valuesSqlList.length === 0){\n\t\t// \treturn sql`insert into ${table} default values ${onConflictSql}${returningSql}`;\n\t\t// }\n\n\t\treturn sql`${withSql}insert into ${table} ${insertOrder} ${valuesSql}${onConflictSql}${returningSql}`;\n\t}\n\n\tsqlToQuery(sql: SQL, invokeSource?: 'indexes' | undefined): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tcasing: this.casing,\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t\tinvokeSource,\n\t\t});\n\t}\n\n\tbuildRelationalQuery({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: SQLiteTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<SQLiteTable, SQLiteColumn> {\n\t\tlet selection: BuildRelationalQueryResult<SQLiteTable, SQLiteColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: SQLiteSelectConfig['orderBy'] = [], where;\n\t\tconst joins: SQLiteSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as SQLiteColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: SQLiteColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as SQLiteColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as SQLiteColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\t// const relationTable = schema[relationTableTsName]!;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQuery({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as SQLiteTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = (sql`(${builtRelation.sql})`).as(selectedRelationTsKey);\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({\n\t\t\t\tmessage:\n\t\t\t\t\t`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\"). You need to have at least one item in \"columns\", \"with\" or \"extras\". If you need to select all columns, omit the \"columns\" key or set it to undefined.`,\n\t\t\t});\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field }) =>\n\t\t\t\t\t\tis(field, SQLiteColumn)\n\t\t\t\t\t\t\t? sql.identifier(this.casing.getColumnCasing(field))\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_group_array(${field}), json_array())`;\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\torderBy,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = undefined;\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, SQLiteTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n\nexport class SQLiteSyncDialect extends SQLiteDialect {\n\tstatic override readonly [entityKind]: string = 'SQLiteSyncDialect';\n\n\tmigrate(\n\t\tmigrations: MigrationMeta[],\n\t\tsession: SQLiteSession<'sync', unknown, Record<string, unknown>, TablesRelationalConfig>,\n\t\tconfig?: string | MigrationConfig,\n\t): void {\n\t\tconst migrationsTable = config === undefined\n\t\t\t? '__drizzle_migrations'\n\t\t\t: typeof config === 'string'\n\t\t\t? '__drizzle_migrations'\n\t\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\n\t\tconst migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t`;\n\t\tsession.run(migrationTableCreate);\n\n\t\tconst dbMigrations = session.values<[number, string, string]>(\n\t\t\tsql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\t\tsession.run(sql`BEGIN`);\n\n\t\ttry {\n\t\t\tfor (const migration of migrations) {\n\t\t\t\tif (!lastDbMigration || Number(lastDbMigration[2])! < migration.folderMillis) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tsession.run(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tsession.run(\n\t\t\t\t\t\tsql`INSERT INTO ${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") VALUES(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tsession.run(sql`COMMIT`);\n\t\t} catch (e) {\n\t\t\tsession.run(sql`ROLLBACK`);\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport class SQLiteAsyncDialect extends SQLiteDialect {\n\tstatic override readonly [entityKind]: string = 'SQLiteAsyncDialect';\n\n\tasync migrate(\n\t\tmigrations: MigrationMeta[],\n\t\tsession: SQLiteSession<'async', any, any, any>,\n\t\tconfig?: string | MigrationConfig,\n\t): Promise<void> {\n\t\tconst migrationsTable = config === undefined\n\t\t\t? '__drizzle_migrations'\n\t\t\t: typeof config === 'string'\n\t\t\t? '__drizzle_migrations'\n\t\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\n\t\tconst migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t`;\n\t\tawait session.run(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.values<[number, string, string]>(\n\t\t\tsql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor (const migration of migrations) {\n\t\t\t\tif (!lastDbMigration || Number(lastDbMigration[2])! < migration.folderMillis) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.run(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.run(\n\t\t\t\t\t\tsql`INSERT INTO ${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") VALUES(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwG;AACxG,oBAA4B;AAE5B,oBAAuB;AACvB,oBAA+B;AAC/B,oBAA6B;AAE7B,uBAWO;AAEP,iBAAwB;AACxB,IAAAA,cAAsE;AACtE,qBAA6B;AAO7B,mBAA4B;AAC5B,sBAAyB;AACzB,IAAAC,gBAAwD;AACxD,mBAAiE;AACjE,yBAA+B;AAO/B,uBAA+B;AAMxB,MAAe,cAAc;AAAA,EACnC,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAG9B;AAAA,EAET,YAAY,QAA8B;AACzC,SAAK,SAAS,IAAI,0BAAY,QAAQ,MAAM;AAAA,EAC7C;AAAA,EAEA,WAAW,MAAsB;AAChC,WAAO,IAAI,IAAI;AAAA,EAChB;AAAA,EAEA,YAAY,MAAsB;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,aAAa,KAAqB;AACjC,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AAAA,EACnC;AAAA,EAEQ,aAAa,SAAkD;AACtE,QAAI,CAAC,SAAS,OAAQ,QAAO;AAE7B,UAAM,gBAAgB,CAAC,sBAAU;AACjC,eAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACvC,oBAAc,KAAK,kBAAM,gBAAI,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,GAAG,GAAG;AACpE,UAAI,IAAI,QAAQ,SAAS,GAAG;AAC3B,sBAAc,KAAK,mBAAO;AAAA,MAC3B;AAAA,IACD;AACA,kBAAc,KAAK,kBAAM;AACzB,WAAO,gBAAI,KAAK,aAAa;AAAA,EAC9B;AAAA,EAEA,iBAAiB,EAAE,OAAO,OAAO,WAAW,UAAU,OAAO,QAAQ,GAA4B;AAChG,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,eAAe,YAClB,6BAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,yBAAa,KAAK,KAAK;AAEhD,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,WAAO,kBAAM,OAAO,eAAe,KAAK,GAAG,QAAQ,GAAG,YAAY,GAAG,UAAU,GAAG,QAAQ;AAAA,EAC3F;AAAA,EAEA,eAAe,OAAoB,KAAqB;AACvD,UAAM,eAAe,MAAM,oBAAM,OAAO,OAAO;AAE/C,UAAM,cAAc,OAAO,KAAK,YAAY,EAAE;AAAA,MAAO,CAAC,YACrD,IAAI,OAAO,MAAM,UAAa,aAAa,OAAO,GAAG,eAAe;AAAA,IACrE;AAEA,UAAM,UAAU,YAAY;AAC5B,WAAO,gBAAI,KAAK,YAAY,QAAQ,CAAC,SAAS,MAAM;AACnD,YAAM,MAAM,aAAa,OAAO;AAEhC,YAAM,QAAQ,IAAI,OAAO,KAAK,gBAAI,MAAM,IAAI,WAAY,GAAG,GAAG;AAC9D,YAAM,MAAM,kBAAM,gBAAI,WAAW,KAAK,OAAO,gBAAgB,GAAG,CAAC,CAAC,MAAM,KAAK;AAE7E,UAAI,IAAI,UAAU,GAAG;AACpB,eAAO,CAAC,KAAK,gBAAI,IAAI,IAAI,CAAC;AAAA,MAC3B;AACA,aAAO,CAAC,GAAG;AAAA,IACZ,CAAC,CAAC;AAAA,EACH;AAAA,EAEA,iBAAiB,EAAE,OAAO,KAAK,OAAO,WAAW,UAAU,OAAO,MAAM,OAAO,QAAQ,GAA4B;AAClH,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,SAAS,KAAK,eAAe,OAAO,GAAG;AAE7C,UAAM,UAAU,QAAQ,gBAAI,KAAK,CAAC,gBAAI,IAAI,QAAQ,GAAG,KAAK,eAAe,IAAI,CAAC,CAAC;AAE/E,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,eAAe,YAClB,6BAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,yBAAa,KAAK,KAAK;AAEhD,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,WAAO,kBAAM,OAAO,UAAU,KAAK,QAAQ,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY,GAAG,UAAU,GAAG,QAAQ;AAAA,EACzH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaQ,eACP,QACA,EAAE,gBAAgB,MAAM,IAAiC,CAAC,GACpD;AACN,UAAM,aAAa,OAAO;AAE1B,UAAM,SAAS,OACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1B,YAAM,QAAoB,CAAC;AAE3B,cAAI,kBAAG,OAAO,gBAAI,OAAO,KAAK,MAAM,kBAAkB;AACrD,cAAM,KAAK,gBAAI,WAAW,MAAM,UAAU,CAAC;AAAA,MAC5C,eAAW,kBAAG,OAAO,gBAAI,OAAO,SAAK,kBAAG,OAAO,eAAG,GAAG;AACpD,cAAM,YAAQ,kBAAG,OAAO,gBAAI,OAAO,IAAI,MAAM,MAAM;AAEnD,YAAI,eAAe;AAClB,gBAAM;AAAA,YACL,IAAI;AAAA,cACH,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5B,wBAAI,kBAAG,GAAG,oBAAM,GAAG;AAClB,yBAAO,gBAAI,WAAW,KAAK,OAAO,gBAAgB,CAAC,CAAC;AAAA,gBACrD;AACA,uBAAO;AAAA,cACR,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,KAAK,KAAK;AAAA,QACjB;AAEA,gBAAI,kBAAG,OAAO,gBAAI,OAAO,GAAG;AAC3B,gBAAM,KAAK,sBAAU,gBAAI,WAAW,MAAM,UAAU,CAAC,EAAE;AAAA,QACxD;AAAA,MACD,eAAW,kBAAG,OAAO,oBAAM,GAAG;AAC7B,cAAM,YAAY,MAAM,MAAM,oBAAM,OAAO,IAAI;AAC/C,YAAI,MAAM,eAAe,uBAAuB;AAC/C,cAAI,eAAe;AAClB,kBAAM,KAAK,uBAAW,gBAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC,WAAW;AAAA,UACpF,OAAO;AACN,kBAAM;AAAA,cACL,uBAAW,gBAAI,WAAW,SAAS,CAAC,IAAI,gBAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC;AAAA,YAC3F;AAAA,UACD;AAAA,QACD,OAAO;AACN,cAAI,eAAe;AAClB,kBAAM,KAAK,gBAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC;AAAA,UAC9D,OAAO;AACN,kBAAM,KAAK,kBAAM,gBAAI,WAAW,SAAS,CAAC,IAAI,gBAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC,EAAE;AAAA,UACnG;AAAA,QACD;AAAA,MACD;AAEA,UAAI,IAAI,aAAa,GAAG;AACvB,cAAM,KAAK,mBAAO;AAAA,MACnB;AAEA,aAAO;AAAA,IACR,CAAC;AAEF,WAAO,gBAAI,KAAK,MAAM;AAAA,EACvB;AAAA,EAEQ,WAAW,OAA8D;AAChF,QAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AACjC,aAAO;AAAA,IACR;AAEA,UAAM,aAAoB,CAAC;AAE3B,QAAI,OAAO;AACV,iBAAW,CAAC,OAAO,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAChD,YAAI,UAAU,GAAG;AAChB,qBAAW,KAAK,kBAAM;AAAA,QACvB;AACA,cAAM,QAAQ,SAAS;AACvB,cAAM,QAAQ,SAAS,KAAK,sBAAU,SAAS,EAAE,KAAK;AAEtD,gBAAI,kBAAG,OAAO,wBAAW,GAAG;AAC3B,gBAAM,YAAY,MAAM,yBAAY,OAAO,IAAI;AAC/C,gBAAM,cAAc,MAAM,yBAAY,OAAO,MAAM;AACnD,gBAAM,gBAAgB,MAAM,yBAAY,OAAO,YAAY;AAC3D,gBAAM,QAAQ,cAAc,gBAAgB,SAAY,SAAS;AACjE,qBAAW;AAAA,YACV,kBAAM,gBAAI,IAAI,SAAS,QAAQ,CAAC,SAAS,cAAc,kBAAM,gBAAI,WAAW,WAAW,CAAC,MAAM,MAAS,GACtG,gBAAI,WAAW,aAAa,CAC7B,GAAG,SAAS,mBAAO,gBAAI,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK;AAAA,UACnD;AAAA,QACD,OAAO;AACN,qBAAW;AAAA,YACV,kBAAM,gBAAI,IAAI,SAAS,QAAQ,CAAC,SAAS,KAAK,GAAG,KAAK;AAAA,UACvD;AAAA,QACD;AACA,YAAI,QAAQ,MAAM,SAAS,GAAG;AAC7B,qBAAW,KAAK,kBAAM;AAAA,QACvB;AAAA,MACD;AAAA,IACD;AAEA,WAAO,gBAAI,KAAK,UAAU;AAAA,EAC3B;AAAA,EAEQ,WAAW,OAA0D;AAC5E,WAAO,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IACxE,yBAAa,KAAK,KAClB;AAAA,EACJ;AAAA,EAEQ,aAAa,SAA4E;AAChG,UAAM,cAAoD,CAAC;AAE3D,QAAI,SAAS;AACZ,iBAAW,CAAC,OAAO,YAAY,KAAK,QAAQ,QAAQ,GAAG;AACtD,oBAAY,KAAK,YAAY;AAE7B,YAAI,QAAQ,QAAQ,SAAS,GAAG;AAC/B,sBAAY,KAAK,mBAAO;AAAA,QACzB;AAAA,MACD;AAAA,IACD;AAEA,WAAO,YAAY,SAAS,IAAI,4BAAgB,gBAAI,KAAK,WAAW,CAAC,KAAK;AAAA,EAC3E;AAAA,EAEQ,eACP,OAC4D;AAC5D,YAAI,kBAAG,OAAO,mBAAK,KAAK,MAAM,oBAAM,OAAO,OAAO,GAAG;AACpD,aAAO,kBAAM,kBAAM,gBAAI,WAAW,MAAM,oBAAM,OAAO,MAAM,KAAK,EAAE,CAAC,IAAI,GAAG,MAAM,oBAAM,OAAO,MAAM,CAAC,CAAC,GACpG,gBAAI,WAAW,MAAM,oBAAM,OAAO,YAAY,CAAC,CAChD,IAAI,gBAAI,WAAW,MAAM,oBAAM,OAAO,IAAI,CAAC,CAAC;AAAA,IAC7C;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,iBACC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GACM;AACN,UAAM,aAAa,kBAAc,kCAAkC,MAAM;AACzE,eAAW,KAAK,YAAY;AAC3B,cACC,kBAAG,EAAE,OAAO,oBAAM,SACf,4BAAa,EAAE,MAAM,KAAK,WACvB,kBAAG,OAAO,wBAAQ,IACpB,MAAM,EAAE,YACR,kBAAG,OAAO,+BAAc,IACxB,MAAM,iCAAc,EAAE,WACtB,kBAAG,OAAO,eAAG,IACb,aACA,4BAAa,KAAK,MACnB,EAAE,CAACC,WACL,OAAO;AAAA,QAAK,CAAC,EAAE,MAAM,MACpB,WAAWA,OAAM,oBAAM,OAAO,OAAO,QAAI,4BAAaA,MAAK,IAAIA,OAAM,oBAAM,OAAO,QAAQ;AAAA,MAC3F,GAAG,EAAE,MAAM,KAAK,GAChB;AACD,cAAM,gBAAY,4BAAa,EAAE,MAAM,KAAK;AAC5C,cAAM,IAAI;AAAA,UACT,SACC,EAAE,KAAK,KAAK,IAAI,CACjB,gCAAgC,SAAS,MAAM,EAAE,MAAM,IAAI,qBAAqB,SAAS;AAAA,QAC1F;AAAA,MACD;AAAA,IACD;AAEA,UAAM,gBAAgB,CAAC,SAAS,MAAM,WAAW;AAEjD,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,cAAc,WAAW,6BAAiB;AAEhD,UAAM,YAAY,KAAK,eAAe,YAAY,EAAE,cAAc,CAAC;AAEnE,UAAM,WAAW,KAAK,eAAe,KAAK;AAE1C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,WAAW,QAAQ,yBAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,0BAAc,MAAM,KAAK;AAEpD,UAAM,cAAiD,CAAC;AACxD,QAAI,SAAS;AACZ,iBAAW,CAAC,OAAO,YAAY,KAAK,QAAQ,QAAQ,GAAG;AACtD,oBAAY,KAAK,YAAY;AAE7B,YAAI,QAAQ,QAAQ,SAAS,GAAG;AAC/B,sBAAY,KAAK,mBAAO;AAAA,QACzB;AAAA,MACD;AAAA,IACD;AAEA,UAAM,aAAa,YAAY,SAAS,IAAI,4BAAgB,gBAAI,KAAK,WAAW,CAAC,KAAK;AAEtF,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,YAAY,SAAS,0BAAc,MAAM,KAAK;AAEpD,UAAM,aACL,kBAAM,OAAO,SAAS,WAAW,IAAI,SAAS,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;AAEnJ,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,KAAK,mBAAmB,YAAY,YAAY;AAAA,IACxD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,mBAAmB,YAAiB,cAAuD;AAC1F,UAAM,CAAC,aAAa,GAAG,IAAI,IAAI;AAE/B,QAAI,CAAC,aAAa;AACjB,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACnE;AAEA,QAAI,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,IAC/D;AAGA,WAAO,KAAK;AAAA,MACX,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,MACvD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,uBAAuB;AAAA,IACtB;AAAA,IACA,aAAa,EAAE,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO;AAAA,EACjE,GAAsF;AAErF,UAAM,YAAY,kBAAM,WAAW,OAAO,CAAC;AAC3C,UAAM,aAAa,kBAAM,YAAY,OAAO,CAAC;AAE7C,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,YAAM,gBAAyC,CAAC;AAIhD,iBAAW,iBAAiB,SAAS;AACpC,gBAAI,kBAAG,eAAe,2BAAY,GAAG;AACpC,wBAAc,KAAK,gBAAI,WAAW,cAAc,IAAI,CAAC;AAAA,QACtD,eAAW,kBAAG,eAAe,eAAG,GAAG;AAClC,mBAAS,IAAI,GAAG,IAAI,cAAc,YAAY,QAAQ,KAAK;AAC1D,kBAAM,QAAQ,cAAc,YAAY,CAAC;AAEzC,oBAAI,kBAAG,OAAO,2BAAY,GAAG;AAC5B,4BAAc,YAAY,CAAC,IAAI,gBAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC;AAAA,YACjF;AAAA,UACD;AAEA,wBAAc,KAAK,kBAAM,aAAa,EAAE;AAAA,QACzC,OAAO;AACN,wBAAc,KAAK,kBAAM,aAAa,EAAE;AAAA,QACzC;AAAA,MACD;AAEA,mBAAa,4BAAgB,gBAAI,KAAK,eAAe,mBAAO,CAAC;AAAA,IAC9D;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,yBAAa,KAAK,KAClB;AAEH,UAAM,gBAAgB,gBAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,EAAE,EAAE;AAE9D,UAAM,YAAY,SAAS,0BAAc,MAAM,KAAK;AAEpD,WAAO,kBAAM,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;AAAA,EACxF;AAAA,EAEA,iBACC,EAAE,OAAO,QAAQ,gBAAgB,YAAY,WAAW,UAAU,OAAO,GACnE;AAEN,UAAM,gBAA8C,CAAC;AACrD,UAAM,UAAwC,MAAM,oBAAM,OAAO,OAAO;AAExE,UAAM,aAAuC,OAAO,QAAQ,OAAO,EAAE;AAAA,MAAO,CAAC,CAAC,GAAG,GAAG,MACnF,CAAC,IAAI,oBAAoB;AAAA,IAC1B;AACA,UAAM,cAAc,WAAW,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,gBAAI,WAAW,KAAK,OAAO,gBAAgB,MAAM,CAAC,CAAC;AAEtG,QAAI,QAAQ;AACX,YAAMC,UAAS;AAEf,cAAI,kBAAGA,SAAQ,eAAG,GAAG;AACpB,sBAAc,KAAKA,OAAM;AAAA,MAC1B,OAAO;AACN,sBAAc,KAAKA,QAAO,OAAO,CAAC;AAAA,MACnC;AAAA,IACD,OAAO;AACN,YAAM,SAAS;AACf,oBAAc,KAAK,gBAAI,IAAI,SAAS,CAAC;AAErC,iBAAW,CAAC,YAAY,KAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,cAAM,YAAgC,CAAC;AACvC,mBAAW,CAAC,WAAW,GAAG,KAAK,YAAY;AAC1C,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,aAAa,cAAc,kBAAG,UAAU,iBAAK,KAAK,SAAS,UAAU,QAAY;AACpF,gBAAI;AACJ,gBAAI,IAAI,YAAY,QAAQ,IAAI,YAAY,QAAW;AACtD,iCAAe,kBAAG,IAAI,SAAS,eAAG,IAAI,IAAI,UAAU,gBAAI,MAAM,IAAI,SAAS,GAAG;AAAA,YAE/E,WAAW,IAAI,cAAc,QAAW;AACvC,oBAAM,kBAAkB,IAAI,UAAU;AACtC,iCAAe,kBAAG,iBAAiB,eAAG,IAAI,kBAAkB,gBAAI,MAAM,iBAAiB,GAAG;AAAA,YAE3F,WAAW,CAAC,IAAI,WAAW,IAAI,eAAe,QAAW;AACxD,oBAAM,mBAAmB,IAAI,WAAW;AACxC,iCAAe,kBAAG,kBAAkB,eAAG,IAAI,mBAAmB,gBAAI,MAAM,kBAAkB,GAAG;AAAA,YAC9F,OAAO;AACN,6BAAe;AAAA,YAChB;AACA,sBAAU,KAAK,YAAY;AAAA,UAC5B,OAAO;AACN,sBAAU,KAAK,QAAQ;AAAA,UACxB;AAAA,QACD;AACA,sBAAc,KAAK,SAAS;AAC5B,YAAI,aAAa,OAAO,SAAS,GAAG;AACnC,wBAAc,KAAK,mBAAO;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAEA,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,YAAY,gBAAI,KAAK,aAAa;AAExC,UAAM,eAAe,YAClB,6BAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,gBAAgB,YAAY,SAC/B,gBAAI,KAAK,UAAU,IACnB;AAMH,WAAO,kBAAM,OAAO,eAAe,KAAK,IAAI,WAAW,IAAI,SAAS,GAAG,aAAa,GAAG,YAAY;AAAA,EACpG;AAAA,EAEA,WAAWC,MAAU,cAAwD;AAC5E,WAAOA,KAAI,QAAQ;AAAA,MAClB,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,qBAAqB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAU0D;AACzD,QAAI,YAAgF,CAAC;AACrF,QAAI,OAAO,QAAQ,UAAyC,CAAC,GAAG;AAChE,UAAM,QAAkC,CAAC;AAEzC,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;AAAA,QACL,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,QACP,WAAO,iCAAmB,OAAuB,UAAU;AAAA,QAC3D,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,CAAC;AAAA,MACb,EAAE;AAAA,IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;AAAA,QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,SAAK,iCAAmB,OAAO,UAAU,CAAC,CAAC;AAAA,MACvG;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,oBAAgB,+BAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,gBAAY,qCAAuB,UAAU,UAAU;AAAA,MAChE;AAEA,YAAM,kBAA0E,CAAC;AACjF,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;AAAA,UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;AAAA,YACjB;AACA,4BAAgB,KAAK,KAAK;AAAA,UAC3B;AAAA,QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,QACnF;AAAA,MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;AAAA,MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;AAAA,MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;AAAA,MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,qBAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;AAAA,YACpB;AAAA,YACA,WAAO,4CAA8B,OAAO,UAAU;AAAA,UACvD,CAAC;AAAA,QACF;AAAA,MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;AAAA,UACd,WAAO,kBAAG,OAAO,gBAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;AAAA,UAC/E;AAAA,UACA,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,UACnE,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,CAAC;AAAA,QACb,CAAC;AAAA,MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,oBAAgB,sCAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;AAAA,MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,gBAAI,kBAAG,cAAc,oBAAM,GAAG;AAC7B,qBAAO,iCAAmB,cAAc,UAAU;AAAA,QACnD;AACA,mBAAO,qCAAuB,cAAc,UAAU;AAAA,MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb;AAAA,MACD,KAAK,mBACJ;AACD,cAAM,yBAAqB,oCAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,wBAAoB,kCAAmB,SAAS,eAAe;AACrE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AAEjE,cAAMC,cAAS;AAAA,UACd,GAAG,mBAAmB,OAAO;AAAA,YAAI,CAACC,QAAO,UACxC;AAAA,kBACC,iCAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;AAAA,kBACxE,iCAAmBA,QAAO,UAAU;AAAA,YACrC;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,qBAAqB;AAAA,UAC/C;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,WAAW,mBAAmB;AAAA,UACrC,aAAa,OAAO,mBAAmB;AAAA,UACvC,iBAAa,kBAAG,UAAU,oBAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;AAAA,UACH,YAAY;AAAA,UACZ,QAAAD;AAAA,UACA,qBAAqB;AAAA,QACtB,CAAC;AACD,cAAM,QAAS,mBAAO,cAAc,GAAG,IAAK,GAAG,qBAAqB;AACpE,kBAAU,KAAK;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,cAAc;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,2BAAa;AAAA,QACtB,SACC,iCAAiC,YAAY,MAAM,OAAO,UAAU;AAAA,MACtE,CAAC;AAAA,IACF;AAEA,QAAI;AAEJ,gBAAQ,gBAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,6BACX,gBAAI;AAAA,QACH,UAAU;AAAA,UAAI,CAAC,EAAE,OAAAC,OAAM,UACtB,kBAAGA,QAAO,2BAAY,IACnB,gBAAI,WAAW,KAAK,OAAO,gBAAgBA,MAAK,CAAC,QACjD,kBAAGA,QAAO,gBAAI,OAAO,IACrBA,OAAM,MACNA;AAAA,QACJ;AAAA,QACA;AAAA,MACD,CACD;AACA,cAAI,kBAAG,qBAAqB,qBAAI,GAAG;AAClC,gBAAQ,4CAAgC,KAAK;AAAA,MAC9C;AACA,YAAM,kBAAkB,CAAC;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,MAAM,GAAG,MAAM;AAAA,QACtB,QAAQ;AAAA,QACR,oBAAoB,YAAY;AAAA,QAChC;AAAA,MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,UAAa,QAAQ,SAAS;AAEtF,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;AAAA,UAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,UACrC,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,YACX;AAAA,cACC,MAAM,CAAC;AAAA,cACP,OAAO,gBAAI,IAAI,GAAG;AAAA,YACnB;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU;AAAA,MACX,OAAO;AACN,qBAAS,2BAAa,OAAO,UAAU;AAAA,MACxC;AAEA,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,kBAAG,QAAQ,wBAAW,IAAI,SAAS,IAAI,yBAAS,QAAQ,CAAC,GAAG,UAAU;AAAA,QAC7E,QAAQ,CAAC;AAAA,QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;AAAA,UAC/C,MAAM,CAAC;AAAA,UACP,WAAO,kBAAGA,QAAO,oBAAM,QAAI,iCAAmBA,QAAO,UAAU,IAAIA;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,QACrC,QAAQ,CAAC;AAAA,QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UACzC,MAAM,CAAC;AAAA,UACP,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN,YAAY,YAAY;AAAA,MACxB,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAA0B,cAAc;AAAA,EACpD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,QACC,YACA,SACA,QACO;AACP,UAAM,kBAAkB,WAAW,SAChC,yBACA,OAAO,WAAW,WAClB,yBACA,OAAO,mBAAmB;AAE7B,UAAM,uBAAuB;AAAA,gCACC,gBAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,YAAQ,IAAI,oBAAoB;AAEhC,UAAM,eAAe,QAAQ;AAAA,MAC5B,mDAAuC,gBAAI,WAAW,eAAe,CAAC;AAAA,IACvE;AAEA,UAAM,kBAAkB,aAAa,CAAC,KAAK;AAC3C,YAAQ,IAAI,sBAAU;AAEtB,QAAI;AACH,iBAAW,aAAa,YAAY;AACnC,YAAI,CAAC,mBAAmB,OAAO,gBAAgB,CAAC,CAAC,IAAK,UAAU,cAAc;AAC7E,qBAAW,QAAQ,UAAU,KAAK;AACjC,oBAAQ,IAAI,gBAAI,IAAI,IAAI,CAAC;AAAA,UAC1B;AACA,kBAAQ;AAAA,YACP,8BACC,gBAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,cAAQ,IAAI,uBAAW;AAAA,IACxB,SAAS,GAAG;AACX,cAAQ,IAAI,yBAAa;AACzB,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,2BAA2B,cAAc;AAAA,EACrD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAM,QACL,YACA,SACA,QACgB;AAChB,UAAM,kBAAkB,WAAW,SAChC,yBACA,OAAO,WAAW,WAClB,yBACA,OAAO,mBAAmB;AAE7B,UAAM,uBAAuB;AAAA,gCACC,gBAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,UAAM,QAAQ,IAAI,oBAAoB;AAEtC,UAAM,eAAe,MAAM,QAAQ;AAAA,MAClC,mDAAuC,gBAAI,WAAW,eAAe,CAAC;AAAA,IACvE;AAEA,UAAM,kBAAkB,aAAa,CAAC,KAAK;AAE3C,UAAM,QAAQ,YAAY,OAAO,OAAO;AACvC,iBAAW,aAAa,YAAY;AACnC,YAAI,CAAC,mBAAmB,OAAO,gBAAgB,CAAC,CAAC,IAAK,UAAU,cAAc;AAC7E,qBAAW,QAAQ,UAAU,KAAK;AACjC,kBAAM,GAAG,IAAI,gBAAI,IAAI,IAAI,CAAC;AAAA,UAC3B;AACA,gBAAM,GAAG;AAAA,YACR,8BACC,gBAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AACD;", "names": ["import_sql", "import_table", "table", "select", "sql", "joinOn", "field"]}
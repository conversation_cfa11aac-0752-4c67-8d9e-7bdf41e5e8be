{"version": 3, "sources": ["../../../src/mysql-core/query-builders/delete.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type {\n\tAnyMySqlQueryResultHKT,\n\tMySqlPreparedQueryConfig,\n\tMySqlQueryResultHKT,\n\tMySqlQueryResultKind,\n\tMySqlSession,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n} from '~/mysql-core/session.ts';\nimport type { MySqlTable } from '~/mysql-core/table.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { Placeholder, Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport type { ValueOrArray } from '~/utils.ts';\nimport type { MySqlColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type { SelectedFieldsOrdered } from './select.types.ts';\n\nexport type MySqlDeleteWithout<\n\tT extends AnyMySqlDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tMySqlDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['preparedQueryHKT'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type MySqlDelete<\n\tTTable extends MySqlTable = MySqlTable,\n\tTQueryResult extends MySqlQueryResultHKT = AnyMySqlQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n> = MySqlDeleteBase<TTable, TQueryResult, TPreparedQueryHKT, true, never>;\n\nexport interface MySqlDeleteConfig {\n\twhere?: SQL | undefined;\n\tlimit?: number | Placeholder;\n\torderBy?: (MySqlColumn | SQL | SQL.Aliased)[];\n\ttable: MySqlTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type MySqlDeletePrepare<T extends AnyMySqlDeleteBase> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tMySqlPreparedQueryConfig & {\n\t\texecute: MySqlQueryResultKind<T['_']['queryResult'], never>;\n\t\titerator: never;\n\t},\n\ttrue\n>;\n\ntype MySqlDeleteDynamic<T extends AnyMySqlDeleteBase> = MySqlDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT']\n>;\n\ntype AnyMySqlDeleteBase = MySqlDeleteBase<any, any, any, any, any>;\n\nexport interface MySqlDeleteBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends MySqlQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<MySqlQueryResultKind<TQueryResult, never>> {\n\treadonly _: {\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t};\n}\n\nexport class MySqlDeleteBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends MySqlQueryResultHKT,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<MySqlQueryResultKind<TQueryResult, never>> implements SQLWrapper {\n\tstatic override readonly [entityKind]: string = 'MySqlDelete';\n\n\tprivate config: MySqlDeleteConfig;\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: MySqlSession,\n\t\tprivate dialect: MySqlDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): MySqlDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\torderBy(\n\t\tbuilder: (deleteTable: TTable) => ValueOrArray<MySqlColumn | SQL | SQL.Aliased>,\n\t): MySqlDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (MySqlColumn | SQL | SQL.Aliased)[]): MySqlDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(deleteTable: TTable) => ValueOrArray<MySqlColumn | SQL | SQL.Aliased>]\n\t\t\t| (MySqlColumn | SQL | SQL.Aliased)[]\n\t): MySqlDeleteWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as any,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t} else {\n\t\t\tconst orderByArray = columns as (MySqlColumn | SQL | SQL.Aliased)[];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\tlimit(limit: number | Placeholder): MySqlDeleteWithout<this, TDynamic, 'limit'> {\n\t\tthis.config.limit = limit;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tprepare(): MySqlDeletePrepare<this> {\n\t\treturn this.session.prepareQuery(\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\ttype: 'delete',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t},\n\t\t) as MySqlDeletePrepare<this>;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this.prepare().execute(placeholderValues);\n\t};\n\n\tprivate createIterator = (): ReturnType<this['prepare']>['iterator'] => {\n\t\tconst self = this;\n\t\treturn async function*(placeholderValues) {\n\t\t\tyield* self.prepare().iterator(placeholderValues);\n\t\t};\n\t};\n\n\titerator = this.createIterator();\n\n\t$dynamic(): MySqlDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAY3B,2BAA6B;AAC7B,6BAAsC;AAGtC,mBAAsB;AAGtB,mBAAiC;AAmE1B,MAAM,wBAQH,kCAA8E;AAAA,EAKvF,YACS,OACA,SACA,SACR,UACC;AACD,UAAM;AALE;AACA;AACA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;AAAA,EACjC;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyCR,MAAM,OAAqE;AAC1E,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAMA,WACI,SAG6C;AAChD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO;AAAA,UACtC,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAChE,WAAK,OAAO,UAAU;AAAA,IACvB,OAAO;AACN,YAAM,eAAe;AACrB,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,OAA0E;AAC/E,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,UAAoC;AACnC,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,YAAQ,+BAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+C;AACvE,UAAM,OAAO;AACb,WAAO,iBAAgB,mBAAmB;AACzC,aAAO,KAAK,QAAQ,EAAE,SAAS,iBAAiB;AAAA,IACjD;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,eAAe;AAAA,EAE/B,WAAqC;AACpC,WAAO;AAAA,EACR;AACD;", "names": []}
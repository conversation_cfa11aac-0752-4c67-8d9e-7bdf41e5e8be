{"version": 3, "sources": ["../../../src/gel-core/columns/bigintT.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelIntColumnBaseBuilder } from './int.common.ts';\n\nexport type GelBigInt64BuilderInitial<TName extends string> = GelBigInt64Builder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'GelBigInt64';\n\tdata: bigint;\n\tdriverParam: bigint;\n\tenumValues: undefined;\n}>;\n\nexport class GelBigInt64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'GelBigInt64'>>\n\textends GelIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelBigInt64Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'GelBigInt64');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelBigInt64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelBigInt64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelBigInt64<T extends ColumnBaseConfig<'bigint', 'GelBigInt64'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelBigInt64';\n\n\tgetSQLType(): string {\n\t\treturn 'edgedbt.bigint_t';\n\t}\n\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value as string); // TODO ts error if remove 'as string'\n\t}\n}\n\nexport function bigintT(): GelBigInt64BuilderInitial<''>;\nexport function bigintT<TName extends string>(name: TName): GelBigInt64BuilderInitial<TName>;\nexport function bigintT(name?: string) {\n\treturn new GelBigInt64Builder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0B;AAC1B,wBAAwC;AAWjC,MAAM,2BACJ,0CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,aAAa;AAAA,EACpC;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,oBAAyE,wBAAa;AAAA,EAClG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAe;AAAA,EAC9B;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,mBAAmB,QAAQ,EAAE;AACzC;", "names": []}
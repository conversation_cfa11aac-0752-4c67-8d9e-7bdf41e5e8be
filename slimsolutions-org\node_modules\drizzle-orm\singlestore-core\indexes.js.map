{"version": 3, "sources": ["../../src/singlestore-core/indexes.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { AnySingleStoreColumn, SingleStoreColumn } from './columns/index.ts';\nimport type { SingleStoreTable } from './table.ts';\n\ninterface IndexConfig {\n\tname: string;\n\n\tcolumns: IndexColumn[];\n\n\t/**\n\t * If true, the index will be created as `create unique index` instead of `create index`.\n\t */\n\tunique?: boolean;\n\n\t/**\n\t * If set, the index will be created as `create index ... using { 'btree' | 'hash' }`.\n\t */\n\tusing?: 'btree' | 'hash';\n\n\t/**\n\t * If set, the index will be created as `create index ... algorythm { 'default' | 'inplace' | 'copy' }`.\n\t */\n\talgorythm?: 'default' | 'inplace' | 'copy';\n\n\t/**\n\t * If set, adds locks to the index creation.\n\t */\n\tlock?: 'default' | 'none' | 'shared' | 'exclusive';\n}\n\nexport type IndexColumn = SingleStoreColumn | SQL;\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'SingleStoreIndexBuilderOn';\n\n\tconstructor(private name: string, private unique: boolean) {}\n\n\ton(...columns: [IndexColumn, ...IndexColumn[]]): IndexBuilder {\n\t\treturn new IndexBuilder(this.name, columns, this.unique);\n\t}\n}\n\nexport interface AnyIndexBuilder {\n\tbuild(table: SingleStoreTable): Index;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IndexBuilder extends AnyIndexBuilder {}\n\nexport class IndexBuilder implements AnyIndexBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreIndexBuilder';\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(name: string, columns: IndexColumn[], unique: boolean) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t};\n\t}\n\n\tusing(using: IndexConfig['using']): this {\n\t\tthis.config.using = using;\n\t\treturn this;\n\t}\n\n\talgorythm(algorythm: IndexConfig['algorythm']): this {\n\t\tthis.config.algorythm = algorythm;\n\t\treturn this;\n\t}\n\n\tlock(lock: IndexConfig['lock']): this {\n\t\tthis.config.lock = lock;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: SingleStoreTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'SingleStoreIndex';\n\n\treadonly config: IndexConfig & { table: SingleStoreTable };\n\n\tconstructor(config: IndexConfig, table: SingleStoreTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport type GetColumnsTableName<TColumns> = TColumns extends\n\tAnySingleStoreColumn<{ tableName: infer TTableName extends string }> | AnySingleStoreColumn<\n\t\t{ tableName: infer TTableName extends string }\n\t>[] ? TTableName\n\t: never;\n\nexport function index(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, false);\n}\n\nexport function uniqueIndex(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, true);\n}\n\n/* export interface AnyFullTextIndexBuilder {\n\tbuild(table: SingleStoreTable): FullTextIndex;\n} */\n/*\ninterface FullTextIndexConfig {\n\tversion?: number;\n}\n\ninterface FullTextIndexFullConfig extends FullTextIndexConfig {\n\tcolumns: IndexColumn[];\n\n\tname: string;\n}\n\nexport class FullTextIndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'SingleStoreFullTextIndexBuilderOn';\n\n\tconstructor(private name: string, private config: FullTextIndexConfig) {}\n\n\ton(...columns: [IndexColumn, ...IndexColumn[]]): FullTextIndexBuilder {\n\t\treturn new FullTextIndexBuilder({\n\t\t\tname: this.name,\n\t\t\tcolumns: columns,\n\t\t\t...this.config,\n\t\t});\n\t}\n} */\n\n/*\nexport interface FullTextIndexBuilder extends AnyFullTextIndexBuilder {}\n\nexport class FullTextIndexBuilder implements AnyFullTextIndexBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreFullTextIndexBuilder'; */\n\n/** @internal */\n/* config: FullTextIndexFullConfig;\n\n\tconstructor(config: FullTextIndexFullConfig) {\n\t\tthis.config = config;\n\t} */\n\n/** @internal */\n/* build(table: SingleStoreTable): FullTextIndex {\n\t\treturn new FullTextIndex(this.config, table);\n\t}\n}\n\nexport class FullTextIndex {\n\tstatic readonly [entityKind]: string = 'SingleStoreFullTextIndex';\n\n\treadonly config: FullTextIndexConfig & { table: SingleStoreTable };\n\n\tconstructor(config: FullTextIndexConfig, table: SingleStoreTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport function fulltext(name: string, config: FullTextIndexConfig): FullTextIndexBuilderOn {\n\treturn new FullTextIndexBuilderOn(name, config);\n}\n\nexport type SortKeyColumn = SingleStoreColumn | SQL;\n\nexport class SortKeyBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreSortKeyBuilder';\n\n\tconstructor(private columns: SortKeyColumn[]) {} */\n\n/** @internal */\n/* build(table: SingleStoreTable): SortKey {\n\t\treturn new SortKey(this.columns, table);\n\t}\n}\n\nexport class SortKey {\n\tstatic readonly [entityKind]: string = 'SingleStoreSortKey';\n\n\tconstructor(public columns: SortKeyColumn[], public table: SingleStoreTable) {}\n}\n\nexport function sortKey(...columns: SortKeyColumn[]): SortKeyBuilder {\n\treturn new SortKeyBuilder(columns);\n}\n */\n"], "mappings": "AAAA,SAAS,kBAAkB;AAiCpB,MAAM,eAAe;AAAA,EAG3B,YAAoB,MAAsB,QAAiB;AAAvC;AAAsB;AAAA,EAAkB;AAAA,EAF5D,QAAiB,UAAU,IAAY;AAAA,EAIvC,MAAM,SAAwD;AAC7D,WAAO,IAAI,aAAa,KAAK,MAAM,SAAS,KAAK,MAAM;AAAA,EACxD;AACD;AASO,MAAM,aAAwC;AAAA,EACpD,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YAAY,MAAc,SAAwB,QAAiB;AAClE,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,OAAmC;AACxC,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAEA,UAAU,WAA2C;AACpD,SAAK,OAAO,YAAY;AACxB,WAAO;AAAA,EACR;AAAA,EAEA,KAAK,MAAiC;AACrC,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAAgC;AACrC,WAAO,IAAI,MAAM,KAAK,QAAQ,KAAK;AAAA,EACpC;AACD;AAEO,MAAM,MAAM;AAAA,EAClB,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EAET,YAAY,QAAqB,OAAyB;AACzD,SAAK,SAAS,EAAE,GAAG,QAAQ,MAAM;AAAA,EAClC;AACD;AAQO,SAAS,MAAM,MAA8B;AACnD,SAAO,IAAI,eAAe,MAAM,KAAK;AACtC;AAEO,SAAS,YAAY,MAA8B;AACzD,SAAO,IAAI,eAAe,MAAM,IAAI;AACrC;", "names": []}
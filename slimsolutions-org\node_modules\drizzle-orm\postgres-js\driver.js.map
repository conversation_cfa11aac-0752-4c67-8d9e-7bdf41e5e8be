{"version": 3, "sources": ["../../src/postgres-js/driver.ts"], "sourcesContent": ["import pgClient, { type Options, type PostgresType, type Sql } from 'postgres';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { PostgresJsQueryResultHKT } from './session.ts';\nimport { PostgresJsSession } from './session.ts';\n\nexport class PostgresJsDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<PostgresJsQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PostgresJsDatabase';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Sql,\n\tconfig: DrizzleConfig<TSchema> = {},\n): PostgresJsDatabase<TSchema> & {\n\t$client: Sql;\n} {\n\tconst transparentParser = (val: any) => val;\n\n\t// Override postgres.js default date parsers: https://github.com/porsager/postgres/discussions/761\n\tfor (const type of ['1184', '1082', '1083', '1114', '1182', '1185', '1115', '1231']) {\n\t\tclient.options.parsers[type as any] = transparentParser;\n\t\tclient.options.serializers[type as any] = transparentParser;\n\t}\n\tclient.options.serializers['114'] = transparentParser;\n\tclient.options.serializers['3802'] = transparentParser;\n\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new PostgresJsSession(client, dialect, schema, { logger, cache: config.cache });\n\tconst db = new PostgresJsDatabase(dialect, session, schema as any) as PostgresJsDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Sql = Sql,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | ({ url?: string } & Options<Record<string, PostgresType>>);\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): PostgresJsDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = pgClient(params[0] as string);\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as {\n\t\t\tconnection?: { url?: string } & Options<Record<string, PostgresType>>;\n\t\t\tclient?: TClient;\n\t\t} & DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tif (typeof connection === 'object' && connection.url !== undefined) {\n\t\t\tconst { url, ...config } = connection;\n\n\t\t\tconst instance = pgClient(url, config);\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = pgClient(connection);\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): PostgresJsDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({\n\t\t\toptions: {\n\t\t\t\tparsers: {},\n\t\t\t\tserializers: {},\n\t\t\t},\n\t\t} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,OAAO,cAA6D;AACpE,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAA6B,gBAAgB;AAE7C,SAAS,yBAAyB;AAE3B,MAAM,2BAEH,WAA8C;AAAA,EACvD,QAA0B,UAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,oBAAoB,CAAC,QAAa;AAGxC,aAAW,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG;AACpF,WAAO,QAAQ,QAAQ,IAAW,IAAI;AACtC,WAAO,QAAQ,YAAY,IAAW,IAAI;AAAA,EAC3C;AACA,SAAO,QAAQ,YAAY,KAAK,IAAI;AACpC,SAAO,QAAQ,YAAY,MAAM,IAAI;AAErC,QAAM,UAAU,IAAI,UAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,kBAAkB,QAAQ,SAAS,QAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAC9F,QAAM,KAAK,IAAI,mBAAmB,SAAS,SAAS,MAAa;AACjE,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAEO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,SAAS,OAAO,CAAC,CAAW;AAE7C,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAKzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAO,eAAe,YAAY,WAAW,QAAQ,QAAW;AACnE,YAAM,EAAE,KAAK,GAAG,OAAO,IAAI;AAE3B,YAAMA,YAAW,SAAS,KAAK,MAAM;AACrC,aAAO,UAAUA,WAAU,aAAa;AAAA,IACzC;AAEA,UAAM,WAAW,SAAS,UAAU;AACpC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU;AAAA,MAChB,SAAS;AAAA,QACR,SAAS,CAAC;AAAA,QACV,aAAa,CAAC;AAAA,MACf;AAAA,IACD,GAAU,MAAM;AAAA,EACjB;AAXO,EAAAA,SAAS;AAAA,GADA;", "names": ["instance", "drizzle"]}
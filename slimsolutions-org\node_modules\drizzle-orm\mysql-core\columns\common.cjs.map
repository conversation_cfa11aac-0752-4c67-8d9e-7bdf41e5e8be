{"version": 3, "sources": ["../../../src/mysql-core/columns/common.ts"], "sourcesContent": ["import { ColumnBuilder } from '~/column-builder.ts';\nimport type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasDefault,\n\tHasGenerated,\n\tIs<PERSON><PERSON>increment,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { ForeignKey, UpdateDeleteAction } from '~/mysql-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/mysql-core/foreign-keys.ts';\nimport type { AnyMySqlTable, MySqlTable } from '~/mysql-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { Update } from '~/utils.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\n\nexport interface ReferenceConfig {\n\tref: () => MySqlColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface MySqlColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'mysql' }> {}\n\nexport interface MySqlGeneratedColumnConfig {\n\tmode?: 'virtual' | 'stored';\n}\n\nexport abstract class MySqlColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string> & {\n\t\tdata: any;\n\t},\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'mysql' }, TExtraConfig>\n\timplements MySqlColumnBuilderBase<T, TTypeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlColumnBuilder';\n\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\treferences(ref: ReferenceConfig['ref'], actions: ReferenceConfig['actions'] = {}): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(name?: string): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL), config?: MySqlGeneratedColumnConfig): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: config?.mode ?? 'virtual',\n\t\t};\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: MySqlColumn, table: MySqlTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn ((ref, actions) => {\n\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t});\n\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t}\n\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t}\n\t\t\t\treturn builder.build(table);\n\t\t\t})(ref, actions);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlColumn<MakeColumnConfig<T, TTableName>>;\n}\n\n// To understand how to use `MySqlColumn` and `AnyMySqlColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class MySqlColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'mysql' }> {\n\tstatic override readonly [entityKind]: string = 'MySqlColumn';\n\n\tconstructor(\n\t\toverride readonly table: MySqlTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type AnyMySqlColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = MySqlColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport interface MySqlColumnWithAutoIncrementConfig {\n\tautoIncrement: boolean;\n}\n\nexport abstract class MySqlColumnBuilderWithAutoIncrement<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends MySqlColumnBuilder<T, TRuntimeConfig & MySqlColumnWithAutoIncrementConfig, TExtraConfig> {\n\tstatic override readonly [entityKind]: string = 'MySqlColumnBuilderWithAutoIncrement';\n\n\tconstructor(name: NonNullable<T['name']>, dataType: T['dataType'], columnType: T['columnType']) {\n\t\tsuper(name, dataType, columnType);\n\t\tthis.config.autoIncrement = false;\n\t}\n\n\tautoincrement(): IsAutoincrement<HasDefault<this>> {\n\t\tthis.config.autoIncrement = true;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as IsAutoincrement<HasDefault<this>>;\n\t}\n}\n\nexport abstract class MySqlColumnWithAutoIncrement<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends MySqlColumn<T, MySqlColumnWithAutoIncrementConfig & TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'MySqlColumnWithAutoIncrement';\n\n\treadonly autoIncrement: boolean = this.config.autoIncrement;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAA8B;AAa9B,oBAAuB;AACvB,oBAA2B;AAE3B,0BAAkC;AAIlC,+BAA8B;AAmBvB,MAAe,2BAOZ,oCAEV;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAExC,oBAAuC,CAAC;AAAA,EAEhD,WAAW,KAA6B,UAAsC,CAAC,GAAS;AACvF,SAAK,kBAAkB,KAAK,EAAE,KAAK,QAAQ,CAAC;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,OAAO,MAAqB;AAC3B,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,kBAAkB,IAAmC,QAElD;AACF,SAAK,OAAO,YAAY;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,MACN,MAAM,QAAQ,QAAQ;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,iBAAiB,QAAqB,OAAiC;AACtE,WAAO,KAAK,kBAAkB,IAAI,CAAC,EAAE,KAAK,QAAQ,MAAM;AACvD,cAAQ,CAACA,MAAKC,aAAY;AACzB,cAAM,UAAU,IAAI,sCAAkB,MAAM;AAC3C,gBAAM,gBAAgBD,KAAI;AAC1B,iBAAO,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,aAAa,EAAE;AAAA,QAC7D,CAAC;AACD,YAAIC,SAAQ,UAAU;AACrB,kBAAQ,SAASA,SAAQ,QAAQ;AAAA,QAClC;AACA,YAAIA,SAAQ,UAAU;AACrB,kBAAQ,SAASA,SAAQ,QAAQ;AAAA,QAClC;AACA,eAAO,QAAQ,MAAM,KAAK;AAAA,MAC3B,GAAG,KAAK,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAMD;AAGO,MAAe,oBAIZ,qBAA8D;AAAA,EAGvE,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,iBAAa,wCAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,IACvD;AACA,UAAM,OAAO,MAAM;AAND;AAAA,EAOnB;AAAA,EAVA,QAA0B,wBAAU,IAAY;AAWjD;AAUO,MAAe,4CAIZ,mBAAyF;AAAA,EAClG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAA8B,UAAyB,YAA6B;AAC/F,UAAM,MAAM,UAAU,UAAU;AAChC,SAAK,OAAO,gBAAgB;AAAA,EAC7B;AAAA,EAEA,gBAAmD;AAClD,SAAK,OAAO,gBAAgB;AAC5B,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAe,qCAGZ,YAAoE;AAAA,EAC7E,QAA0B,wBAAU,IAAY;AAAA,EAEvC,gBAAyB,KAAK,OAAO;AAC/C;", "names": ["ref", "actions"]}
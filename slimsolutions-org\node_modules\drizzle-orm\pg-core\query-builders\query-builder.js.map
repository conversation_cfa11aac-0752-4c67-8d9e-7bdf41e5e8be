{"version": 3, "sources": ["../../../src/pg-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { PgDialectConfig } from '~/pg-core/dialect.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { PgColumn } from '../columns/index.ts';\nimport type { WithBuilder } from '../subquery.ts';\nimport { PgSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'PgQueryBuilder';\n\n\tprivate dialect: PgDialect | undefined;\n\tprivate dialectConfig: PgDialectConfig | undefined;\n\n\tconstructor(dialect?: PgDialect | PgDialectConfig) {\n\t\tthis.dialect = is(dialect, PgDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, PgDialect) ? undefined : dialect;\n\t}\n\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst queryBuilder = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(queryBuilder);\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t) as any;\n\t\t};\n\t\treturn { as };\n\t};\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): PgSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: TSelection,\n\t): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new PgDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAE/B,SAAS,iBAAiB;AAE1B,SAAS,6BAA6B;AAEtC,SAAS,oBAAoB;AAG7B,SAAS,uBAAuB;AAGzB,MAAM,aAAa;AAAA,EACzB,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAER,YAAY,SAAuC;AAClD,SAAK,UAAU,GAAG,SAAS,SAAS,IAAI,UAAU;AAClD,SAAK,gBAAgB,GAAG,SAAS,SAAS,IAAI,SAAY;AAAA,EAC3D;AAAA,EAEA,QAAqB,CAAC,OAAe,cAAiC;AACrE,UAAM,eAAe;AACrB,UAAM,KAAK,CACV,OAII;AACJ,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK,GAAG,YAAY;AAAA,MACrB;AAEA,aAAO,IAAI;AAAA,QACV,IAAI;AAAA,UACH,GAAG,OAAO;AAAA,UACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,QACA,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,MACvF;AAAA,IACD;AACA,WAAO,EAAE,GAAG;AAAA,EACb;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAIb,aAAS,OACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAIA,aAAS,eACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAOA,aAAS,iBACR,IACA,QACgD;AAChD,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU,EAAE,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,gBAAgB,iBAAiB;AAAA,EACnD;AAAA,EAIA,OAA0C,QAAoE;AAC7G,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAIA,eAAkD,QAA8D;AAC/G,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA,EAOA,iBACC,IACA,QAC0C;AAC1C,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU,EAAE,GAAG;AAAA,IAChB,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,UAAU,KAAK,aAAa;AAAA,IAChD;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}
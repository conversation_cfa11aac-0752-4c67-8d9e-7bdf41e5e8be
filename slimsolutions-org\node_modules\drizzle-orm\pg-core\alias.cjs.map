{"version": 3, "sources": ["../../src/pg-core/alias.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\n\nimport type { PgTable } from './table.ts';\nimport type { PgViewBase } from './view-base.ts';\n\nexport function alias<TTable extends PgTable | PgViewBase, TAlias extends string>(\n\ttable: TTable,\n\talias: T<PERSON><PERSON><PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAuC;AAMhC,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,oCAAuBA,QAAO,KAAK,CAAC;AACjE;", "names": ["alias"]}
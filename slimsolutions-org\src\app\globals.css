@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1f2937;
  --primary: #059669;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --border: #e5e7eb;
  --input: #ffffff;
  --ring: #059669;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Font families */
  --font-sans: var(--font-open-sans), "Arial", "Helvetica", sans-serif;
  --font-heading: var(--font-playfair), "Georgia", serif;
  --font-ui: var(--font-inter), "Arial", "Helvetica", sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary: #10b981;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #cbd5e1;
    --accent: #059669;
    --accent-foreground: #ffffff;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #334155;
    --input: #1e293b;
    --ring: #10b981;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: 1.4;
}

/* Typography classes */
.font-heading {
  font-family: var(--font-heading);
}

.font-ui {
  font-family: var(--font-ui);
}

/* Heading styles */
h1 {
  font-family: var(--font-heading);
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
  line-height: 1.2;
}

h2 {
  font-family: var(--font-heading);
  font-size: 1.75rem; /* 28px */
  font-weight: 600;
  line-height: 1.3;
}

h3 {
  font-family: var(--font-heading);
  font-size: 1.375rem; /* 22px */
  font-weight: 600;
  line-height: 1.3;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

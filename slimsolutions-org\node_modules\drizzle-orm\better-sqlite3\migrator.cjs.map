{"version": 3, "sources": ["../../src/better-sqlite3/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport type { BetterSQLite3Database } from './driver.ts';\n\nexport function migrate<TSchema extends Record<string, unknown>>(\n\tdb: BetterSQLite3Database<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tdb.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AAG5B,SAAS,QACf,IACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAC5C,KAAG,QAAQ,QAAQ,YAAY,GAAG,SAAS,MAAM;AAClD;", "names": []}
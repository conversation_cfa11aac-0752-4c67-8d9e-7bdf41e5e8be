{"version": 3, "sources": ["../../../src/singlestore-core/columns/int.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreIntBuilderInitial<TName extends string> = SingleStoreIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreInt'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreIntBuilder';\n\n\tconstructor(name: T['name'], config?: SingleStoreIntConfig) {\n\t\tsuper(name, 'number', 'SingleStoreInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreInt<T extends ColumnBaseConfig<'number', 'SingleStoreInt'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreInt';\n\n\tgetSQLType(): string {\n\t\treturn `int${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport interface SingleStoreIntConfig {\n\tunsigned?: boolean;\n}\n\nexport function int(): SingleStoreIntBuilderInitial<''>;\nexport function int(\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreIntBuilderInitial<''>;\nexport function int<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreIntBuilderInitial<TName>;\nexport function int(a?: string | SingleStoreIntConfig, b?: SingleStoreIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreIntConfig>(a, b);\n\treturn new SingleStoreIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA8F;AAYvF,MAAM,8BACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,MAAM,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACrD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAcO,SAAS,IAAI,GAAmC,GAA0B;AAChF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}
{"version": 3, "sources": ["../../../src/pg-core/columns/jsonb.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgJsonbBuilderInitial<TName extends string> = PgJsonbBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgJsonb';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class PgJsonbBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgJsonb'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgJsonbBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgJsonb');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgJsonb<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgJsonb<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgJsonb<T extends ColumnBaseConfig<'json', 'PgJsonb'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgJsonb';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgJsonbBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'jsonb';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: T['data'] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(value);\n\t\t\t} catch {\n\t\t\t\treturn value as T['data'];\n\t\t\t}\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function jsonb(): PgJsonbBuilderInitial<''>;\nexport function jsonb<TName extends string>(name: TName): PgJsonbBuilderInitial<TName>;\nexport function jsonb(name?: string) {\n\treturn new PgJsonbBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAWnC,MAAM,uBAA6E,8BAAmB;AAAA,EAC5G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,SAAS;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;AAAA,EAC/G;AACD;AAEO,MAAM,gBAA+D,uBAAY;AAAA,EACvF,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,OAA6C,QAAqC;AAC7F,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAAsC;AACjE,QAAI,OAAO,UAAU,UAAU;AAC9B,UAAI;AACH,eAAO,KAAK,MAAM,KAAK;AAAA,MACxB,QAAQ;AACP,eAAO;AAAA,MACR;AAAA,IACD;AACA,WAAO;AAAA,EACR;AACD;AAIO,SAAS,MAAM,MAAe;AACpC,SAAO,IAAI,eAAe,QAAQ,EAAE;AACrC;", "names": []}
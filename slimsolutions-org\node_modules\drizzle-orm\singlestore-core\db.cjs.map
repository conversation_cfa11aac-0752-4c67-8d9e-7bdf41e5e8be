{"version": 3, "sources": ["../../src/singlestore-core/db.ts"], "sourcesContent": ["import type { ResultSetHeader } from 'mysql2/promise';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { SingleStoreDriverDatabase } from '~/singlestore/driver.ts';\nimport { type ColumnsSelection, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { SingleStoreDialect } from './dialect.ts';\nimport { SingleStoreCountBuilder } from './query-builders/count.ts';\nimport {\n\tQueryBuilder,\n\tSingleStoreDeleteBase,\n\tSingleStoreInsertBuilder,\n\tSingleStoreSelectBuilder,\n\tSingleStoreUpdateBuilder,\n} from './query-builders/index.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type {\n\tPreparedQueryHKTBase,\n\tSingleStoreQueryResultHKT,\n\tSingleStoreQueryResultKind,\n\tSingleStoreSession,\n\tSingleStoreTransaction,\n\tSingleStoreTransactionConfig,\n} from './session.ts';\nimport type { WithBuilder } from './subquery.ts';\nimport type { SingleStoreTable } from './table.ts';\n\nexport class SingleStoreDatabase<\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTFullSchema extends Record<string, unknown> = {},\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'SingleStoreDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t};\n\n\t// We are waiting for SingleStore support for `json_array` function\n\t/**@inrernal */\n\tquery: unknown;\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: SingleStoreDialect,\n\t\t/** @internal */\n\t\treadonly session: SingleStoreSession<any, any, any, any>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\t// this.queryNotSupported = true;\n\t\t// if (this._.schema) {\n\t\t// \tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t// \t\t(this.query as SingleStoreDatabase<TQueryResult, TPreparedQueryHKT, Record<string, any>>['query'])[tableName] =\n\t\t// \t\t\tnew RelationalQueryBuilder(\n\t\t// \t\t\t\tschema!.fullSchema,\n\t\t// \t\t\t\tthis._.schema,\n\t\t// \t\t\t\tthis._.tableNamesMap,\n\t\t// \t\t\t\tschema!.fullSchema[tableName] as SingleStoreTable,\n\t\t// \t\t\t\tcolumns,\n\t\t// \t\t\t\tdialect,\n\t\t// \t\t\t\tsession,\n\t\t// \t\t\t);\n\t\t// \t}\n\t\t// }\n\t\tthis.$cache = { invalidate: async (_params: any) => {} };\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst self = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(new QueryBuilder(self.dialect));\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t);\n\t\t};\n\t\treturn { as };\n\t};\n\n\t$count(\n\t\tsource: SingleStoreTable | SQL | SQLWrapper, // SingleStoreViewBase |\n\t\tfilters?: SQL<unknown>,\n\t) {\n\t\treturn new SingleStoreCountBuilder({ source, filters, session: this.session });\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): SingleStoreSelectBuilder<undefined, TPreparedQueryHKT>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection, TPreparedQueryHKT>;\n\t\tfunction select(fields?: SelectedFields): SingleStoreSelectBuilder<SelectedFields | undefined, TPreparedQueryHKT> {\n\t\t\treturn new SingleStoreSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): SingleStoreSelectBuilder<undefined, TPreparedQueryHKT>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection, TPreparedQueryHKT>;\n\t\tfunction selectDistinct(\n\t\t\tfields?: SelectedFields,\n\t\t): SingleStoreSelectBuilder<SelectedFields | undefined, TPreparedQueryHKT> {\n\t\t\treturn new SingleStoreSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends SingleStoreTable>(\n\t\t\ttable: TTable,\n\t\t): SingleStoreUpdateBuilder<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\t\treturn new SingleStoreUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends SingleStoreTable>(\n\t\t\ttable: TTable,\n\t\t): SingleStoreDeleteBase<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\t\treturn new SingleStoreDeleteBase(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, update, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): SingleStoreSelectBuilder<undefined, TPreparedQueryHKT>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SingleStoreSelectBuilder<TSelection, TPreparedQueryHKT>;\n\tselect(fields?: SelectedFields): SingleStoreSelectBuilder<SelectedFields | undefined, TPreparedQueryHKT> {\n\t\treturn new SingleStoreSelectBuilder({ fields: fields ?? undefined, session: this.session, dialect: this.dialect });\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): SingleStoreSelectBuilder<undefined, TPreparedQueryHKT>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SingleStoreSelectBuilder<TSelection, TPreparedQueryHKT>;\n\tselectDistinct(fields?: SelectedFields): SingleStoreSelectBuilder<SelectedFields | undefined, TPreparedQueryHKT> {\n\t\treturn new SingleStoreSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t * ```\n\t */\n\tupdate<TTable extends SingleStoreTable>(\n\t\ttable: TTable,\n\t): SingleStoreUpdateBuilder<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\treturn new SingleStoreUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t * ```\n\t */\n\tinsert<TTable extends SingleStoreTable>(\n\t\ttable: TTable,\n\t): SingleStoreInsertBuilder<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\treturn new SingleStoreInsertBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t * ```\n\t */\n\tdelete<TTable extends SingleStoreTable>(\n\t\ttable: TTable,\n\t): SingleStoreDeleteBase<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\treturn new SingleStoreDeleteBase(table, this.session, this.dialect);\n\t}\n\n\texecute<T extends { [column: string]: any } = ResultSetHeader>(\n\t\tquery: SQLWrapper | string,\n\t): Promise<SingleStoreQueryResultKind<TQueryResult, T>> {\n\t\treturn this.session.execute(typeof query === 'string' ? sql.raw(query) : query.getSQL());\n\t}\n\n\t$cache: { invalidate: Cache['onMutate'] };\n\n\ttransaction<T>(\n\t\ttransaction: (\n\t\t\ttx: SingleStoreTransaction<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema>,\n\t\t\tconfig?: SingleStoreTransactionConfig,\n\t\t) => Promise<T>,\n\t\tconfig?: SingleStoreTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.session.transaction(transaction, config);\n\t}\n}\n\nexport type SingleStoreWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tQ extends SingleStoreDriverDatabase,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): SingleStoreWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst $count: Q['$count'] = (...args: [any]) => getReplica(replicas).$count(...args);\n\tconst $with: Q['with'] = (...args: []) => getReplica(replicas).with(...args);\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst execute: Q['execute'] = (...args: [any]) => primary.execute(...args);\n\tconst transaction: Q['transaction'] = (...args: [any, any]) => primary.transaction(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\texecute,\n\t\ttransaction,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\t$count,\n\t\twith: $with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,6BAAsC;AAEtC,iBAAsE;AACtE,sBAA6B;AAE7B,mBAAwC;AACxC,4BAMO;AAaA,MAAM,oBAKX;AAAA,EAaD,YAEU,SAEA,SACT,QACC;AAJQ;AAEA;AAGT,SAAK,IAAI,SACN;AAAA,MACD,QAAQ,OAAO;AAAA,MACf,YAAY,OAAO;AAAA,MACnB,eAAe,OAAO;AAAA,IACvB,IACE;AAAA,MACD,QAAQ;AAAA,MACR,YAAY,CAAC;AAAA,MACb,eAAe,CAAC;AAAA,IACjB;AACD,SAAK,QAAQ,CAAC;AAgBd,SAAK,SAAS,EAAE,YAAY,OAAO,YAAiB;AAAA,IAAC,EAAE;AAAA,EACxD;AAAA,EA/CA,QAAiB,wBAAU,IAAY;AAAA;AAAA;AAAA,EAUvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuEA,QAAqB,CAAC,OAAe,cAAiC;AACrE,UAAM,OAAO;AACb,UAAM,KAAK,CACV,OAII;AACJ,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK,GAAG,IAAI,mCAAa,KAAK,OAAO,CAAC;AAAA,MACvC;AAEA,aAAO,IAAI;AAAA,QACV,IAAI;AAAA,UACH,GAAG,OAAO;AAAA,UACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,QACA,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,MACvF;AAAA,IACD;AACA,WAAO,EAAE,GAAG;AAAA,EACb;AAAA,EAEA,OACC,QACA,SACC;AACD,WAAO,IAAI,qCAAwB,EAAE,QAAQ,SAAS,SAAS,KAAK,QAAQ,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AA0Cb,aAAS,OAAO,QAAkG;AACjH,aAAO,IAAI,+CAAyB;AAAA,QACnC,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AA8BA,aAAS,eACR,QAC0E;AAC1E,aAAO,IAAI,+CAAyB;AAAA,QACnC,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAuBA,aAAS,OACR,OACoE;AACpE,aAAO,IAAI,+CAAyB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IAC/E;AAqBA,aAAS,QACR,OACiE;AACjE,aAAO,IAAI,4CAAsB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IAC5E;AAEA,WAAO,EAAE,QAAQ,gBAAgB,QAAQ,QAAQ,QAAQ;AAAA,EAC1D;AAAA,EA0CA,OAAO,QAAkG;AACxG,WAAO,IAAI,+CAAyB,EAAE,QAAQ,UAAU,QAAW,SAAS,KAAK,SAAS,SAAS,KAAK,QAAQ,CAAC;AAAA,EAClH;AAAA,EA8BA,eAAe,QAAkG;AAChH,WAAO,IAAI,+CAAyB;AAAA,MACnC,QAAQ,UAAU;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,OACC,OACoE;AACpE,WAAO,IAAI,+CAAyB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OACC,OACoE;AACpE,WAAO,IAAI,+CAAyB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OACC,OACiE;AACjE,WAAO,IAAI,4CAAsB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACnE;AAAA,EAEA,QACC,OACuD;AACvD,WAAO,KAAK,QAAQ,QAAQ,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO,CAAC;AAAA,EACxF;AAAA,EAEA;AAAA,EAEA,YACC,aAIA,QACa;AACb,WAAO,KAAK,QAAQ,YAAY,aAAa,MAAM;AAAA,EACpD;AACD;AAIO,MAAM,eAAe,CAG3B,SACA,UACA,aAAmC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,MAC7D;AAChC,QAAM,SAAsB,IAAI,SAAa,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AAChF,QAAM,iBAAsC,IAAI,SAAa,WAAW,QAAQ,EAAE,eAAe,GAAG,IAAI;AACxG,QAAM,SAAsB,IAAI,SAAgB,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AACnF,QAAM,QAAmB,IAAI,SAAa,WAAW,QAAQ,EAAE,KAAK,GAAG,IAAI;AAE3E,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,UAAuB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACvE,QAAM,UAAwB,IAAI,SAAgB,QAAQ,QAAQ,GAAG,IAAI;AACzE,QAAM,cAAgC,IAAI,SAAqB,QAAQ,YAAY,GAAG,IAAI;AAE1F,SAAO;AAAA,IACN,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,IAAI,QAAQ;AACX,aAAO,WAAW,QAAQ,EAAE;AAAA,IAC7B;AAAA,EACD;AACD;", "names": []}
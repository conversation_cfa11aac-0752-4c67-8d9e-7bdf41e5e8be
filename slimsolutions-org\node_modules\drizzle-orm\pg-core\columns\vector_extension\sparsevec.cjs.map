{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/sparsevec.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgSparseVectorBuilderInitial<TName extends string> = PgSparseVectorBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgSparseVector';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgSparseVectorBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgSparseVector'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: number | undefined }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgSparseVectorBuilder';\n\n\tconstructor(name: string, config: PgSparseVectorConfig) {\n\t\tsuper(name, 'string', 'PgSparseVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSparseVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSparseVector<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgSparseVector<T extends ColumnBaseConfig<'string', 'PgSparseVector'>>\n\textends PgColumn<T, { dimensions: number | undefined }>\n{\n\tstatic override readonly [entityKind]: string = 'PgSparseVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `sparsevec(${this.dimensions})`;\n\t}\n}\n\nexport interface PgSparseVectorConfig {\n\tdimensions: number;\n}\n\nexport function sparsevec(\n\tconfig: PgSparseVectorConfig,\n): PgSparseVectorBuilderInitial<''>;\nexport function sparsevec<TName extends string>(\n\tname: TName,\n\tconfig: PgSparseVectorConfig,\n): PgSparseVectorBuilderInitial<TName>;\nexport function sparsevec(a: string | PgSparseVectorConfig, b?: PgSparseVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgSparseVectorConfig>(a, b);\n\treturn new PgSparseVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA0C;AAWnC,MAAM,8BACJ,8BAIT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc,QAA8B;AACvD,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,aAAa,KAAK,OAAO;AAAA,EAElC,aAAqB;AACpB,WAAO,aAAa,KAAK,UAAU;AAAA,EACpC;AACD;AAaO,SAAS,UAAU,GAAkC,GAA0B;AACrF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}
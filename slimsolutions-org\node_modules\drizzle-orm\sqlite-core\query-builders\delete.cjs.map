{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/delete.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { Placeholder, Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { SQLiteDialect } from '~/sqlite-core/dialect.ts';\nimport type { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { SQLiteTable } from '~/sqlite-core/table.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { type DrizzleTypeError, orderSelectedFields, type ValueOrArray } from '~/utils.ts';\nimport type { SQLiteColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\n\nexport type SQLiteDeleteWithout<\n\tT extends AnySQLiteDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tSQLiteDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['resultType'],\n\t\t\tT['_']['runResult'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type SQLiteDelete<\n\tTTable extends SQLiteTable = SQLiteTable,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n> = SQLiteDeleteBase<TTable, TResultType, TRunResult, TReturning, true, never>;\n\nexport interface SQLiteDeleteConfig {\n\twhere?: SQL | undefined;\n\tlimit?: number | Placeholder;\n\torderBy?: (SQLiteColumn | SQL | SQL.Aliased)[];\n\ttable: SQLiteTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type SQLiteDeleteReturningAll<\n\tT extends AnySQLiteDeleteBase,\n\tTDynamic extends boolean,\n> = SQLiteDeleteWithout<\n\tSQLiteDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tT['_']['dynamic'],\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteDeleteReturning<\n\tT extends AnySQLiteDeleteBase,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = SQLiteDeleteWithout<\n\tSQLiteDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tT['_']['dynamic'],\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteDeleteExecute<T extends AnySQLiteDeleteBase> = T['_']['returning'] extends undefined\n\t? T['_']['runResult']\n\t: T['_']['returning'][];\n\nexport type SQLiteDeletePrepare<T extends AnySQLiteDeleteBase> = SQLitePreparedQuery<{\n\ttype: T['_']['resultType'];\n\trun: T['_']['runResult'];\n\tall: T['_']['returning'] extends undefined ? DrizzleTypeError<'.all() cannot be used without .returning()'>\n\t\t: T['_']['returning'][];\n\tget: T['_']['returning'] extends undefined ? DrizzleTypeError<'.get() cannot be used without .returning()'>\n\t\t: T['_']['returning'] | undefined;\n\tvalues: T['_']['returning'] extends undefined ? DrizzleTypeError<'.values() cannot be used without .returning()'>\n\t\t: any[][];\n\texecute: SQLiteDeleteExecute<T>;\n}>;\n\nexport type SQLiteDeleteDynamic<T extends AnySQLiteDeleteBase> = SQLiteDelete<\n\tT['_']['table'],\n\tT['_']['resultType'],\n\tT['_']['runResult'],\n\tT['_']['returning']\n>;\n\nexport type AnySQLiteDeleteBase = SQLiteDeleteBase<any, any, any, any, any, any>;\n\nexport interface SQLiteDeleteBase<\n\tTTable extends SQLiteTable,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? TRunResult : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? TRunResult : TReturning[], 'sqlite'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\tdialect: 'sqlite';\n\t\treadonly table: TTable;\n\t\treadonly resultType: TResultType;\n\t\treadonly runResult: TRunResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? TRunResult : TReturning[];\n\t};\n}\n\nexport class SQLiteDeleteBase<\n\tTTable extends SQLiteTable,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? TRunResult : TReturning[]>\n\timplements RunnableQuery<TReturning extends undefined ? TRunResult : TReturning[], 'sqlite'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteDelete';\n\n\t/** @internal */\n\tconfig: SQLiteDeleteConfig;\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: SQLiteSession<any, any, any, any>,\n\t\tprivate dialect: SQLiteDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): SQLiteDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\torderBy(\n\t\tbuilder: (deleteTable: TTable) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>,\n\t): SQLiteDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (SQLiteColumn | SQL | SQL.Aliased)[]): SQLiteDeleteWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(deleteTable: TTable) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>]\n\t\t\t| (SQLiteColumn | SQL | SQL.Aliased)[]\n\t): SQLiteDeleteWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as any,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t} else {\n\t\t\tconst orderByArray = columns as (SQLiteColumn | SQL | SQL.Aliased)[];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\tlimit(limit: number | Placeholder): SQLiteDeleteWithout<this, TDynamic, 'limit'> {\n\t\tthis.config.limit = limit;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the deleted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete#delete-with-return}\n\t *\n\t * @example\n\t * ```ts\n\t * // Delete all cars with the green color and return all fields\n\t * const deletedCars: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Delete all cars with the green color and return only their id and brand fields\n\t * const deletedCarsIdsAndBrands: { id: number, brand: string }[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): SQLiteDeleteReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): SQLiteDeleteReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.table[SQLiteTable.Symbol.Columns],\n\t): SQLiteDeleteReturning<this, TDynamic, any> {\n\t\tthis.config.returning = orderSelectedFields<SQLiteColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(isOneTimeQuery = true): SQLiteDeletePrepare<this> {\n\t\treturn this.session[isOneTimeQuery ? 'prepareOneTimeQuery' : 'prepareQuery'](\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t\tthis.config.returning ? 'all' : 'run',\n\t\t\ttrue,\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\ttype: 'delete',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t},\n\t\t) as SQLiteDeletePrepare<this>;\n\t}\n\n\tprepare(): SQLiteDeletePrepare<this> {\n\t\treturn this._prepare(false);\n\t}\n\n\trun: ReturnType<this['prepare']>['run'] = (placeholderValues) => {\n\t\treturn this._prepare().run(placeholderValues);\n\t};\n\n\tall: ReturnType<this['prepare']>['all'] = (placeholderValues) => {\n\t\treturn this._prepare().all(placeholderValues);\n\t};\n\n\tget: ReturnType<this['prepare']>['get'] = (placeholderValues) => {\n\t\treturn this._prepare().get(placeholderValues);\n\t};\n\n\tvalues: ReturnType<this['prepare']>['values'] = (placeholderValues) => {\n\t\treturn this._prepare().values(placeholderValues);\n\t};\n\n\toverride async execute(placeholderValues?: Record<string, unknown>): Promise<SQLiteDeleteExecute<this>> {\n\t\treturn this._prepare().execute(placeholderValues) as SQLiteDeleteExecute<this>;\n\t}\n\n\t$dynamic(): SQLiteDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,2BAA6B;AAE7B,6BAAsC;AAItC,mBAA4B;AAE5B,IAAAA,gBAAsB;AACtB,mBAA8E;AAE9E,IAAAC,gBAAiC;AAsH1B,MAAM,yBASH,kCAEV;AAAA,EAMC,YACS,OACA,SACA,SACR,UACC;AACD,UAAM;AALE;AACA;AACA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;AAAA,EACjC;AAAA,EAbA,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyCA,MAAM,OAAsE;AAC3E,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAMA,WACI,SAG8C;AACjD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO,MAAM,oBAAM,OAAO,OAAO;AAAA,UACtC,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAChE,WAAK,OAAO,UAAU;AAAA,IACvB,OAAO;AACN,YAAM,eAAe;AACrB,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,OAA2E;AAChF,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA0BA,UACC,SAA6B,KAAK,MAAM,yBAAY,OAAO,OAAO,GACrB;AAC7C,SAAK,OAAO,gBAAY,kCAAkC,MAAM;AAChE,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,iBAAiB,MAAiC;AAC1D,WAAO,KAAK,QAAQ,iBAAiB,wBAAwB,cAAc;AAAA,MAC1E,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO,YAAY,QAAQ;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,YAAQ,gCAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAAA,EAEA,UAAqC;AACpC,WAAO,KAAK,SAAS,KAAK;AAAA,EAC3B;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,SAAgD,CAAC,sBAAsB;AACtE,WAAO,KAAK,SAAS,EAAE,OAAO,iBAAiB;AAAA,EAChD;AAAA,EAEA,MAAe,QAAQ,mBAAiF;AACvG,WAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,EACjD;AAAA,EAEA,WAAsC;AACrC,WAAO;AAAA,EACR;AACD;", "names": ["import_table", "import_utils"]}
{"version": 3, "sources": ["../../../src/gel-core/columns/date.common.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnDataType } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { GelColumnBuilder } from './common.ts';\n\nexport abstract class GelLocalDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends GelColumnBuilder<T, TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'GelLocalDateColumnBaseBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,iBAAoB;AACpB,oBAAiC;AAE1B,MAAe,sCAGZ,+BAAoC;AAAA,EAC7C,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAa;AACZ,WAAO,KAAK,QAAQ,qBAAU;AAAA,EAC/B;AACD;", "names": []}
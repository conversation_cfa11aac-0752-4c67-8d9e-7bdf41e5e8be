{"version": 3, "sources": ["../../../src/mysql-core/columns/timestamp.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlDateBaseColumn, MySqlDateColumnBaseBuilder } from './date.common.ts';\n\nexport type MySqlTimestampBuilderInitial<TName extends string> = MySqlTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'MySqlTimestamp';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'MySqlTimestamp'>>\n\textends MySqlDateColumnBaseBuilder<T, MySqlTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlTimestampBuilder';\n\n\tconstructor(name: T['name'], config: MySqlTimestampConfig | undefined) {\n\t\tsuper(name, 'date', 'MySqlTimestamp');\n\t\tthis.config.fsp = config?.fsp;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlTimestamp<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlTimestamp<T extends ColumnBaseConfig<'date', 'MySqlTimestamp'>>\n\textends MySqlDateBaseColumn<T, MySqlTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlTimestamp';\n\n\treadonly fsp: number | undefined = this.config.fsp;\n\n\tgetSQLType(): string {\n\t\tconst precision = this.fsp === undefined ? '' : `(${this.fsp})`;\n\t\treturn `timestamp${precision}`;\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value + '+0000');\n\t}\n\n\toverride mapToDriverValue(value: Date): string {\n\t\treturn value.toISOString().slice(0, -1).replace('T', ' ');\n\t}\n}\n\nexport type MySqlTimestampStringBuilderInitial<TName extends string> = MySqlTimestampStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlTimestampString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlTimestampStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlTimestampString'>>\n\textends MySqlDateColumnBaseBuilder<T, MySqlTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlTimestampStringBuilder';\n\n\tconstructor(name: T['name'], config: MySqlTimestampConfig | undefined) {\n\t\tsuper(name, 'string', 'MySqlTimestampString');\n\t\tthis.config.fsp = config?.fsp;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlTimestampString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlTimestampString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlTimestampString<T extends ColumnBaseConfig<'string', 'MySqlTimestampString'>>\n\textends MySqlDateBaseColumn<T, MySqlTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlTimestampString';\n\n\treadonly fsp: number | undefined = this.config.fsp;\n\n\tgetSQLType(): string {\n\t\tconst precision = this.fsp === undefined ? '' : `(${this.fsp})`;\n\t\treturn `timestamp${precision}`;\n\t}\n}\n\nexport type TimestampFsp = 0 | 1 | 2 | 3 | 4 | 5 | 6;\n\nexport interface MySqlTimestampConfig<TMode extends 'string' | 'date' = 'string' | 'date'> {\n\tmode?: TMode;\n\tfsp?: TimestampFsp;\n}\n\nexport function timestamp(): MySqlTimestampBuilderInitial<''>;\nexport function timestamp<TMode extends MySqlTimestampConfig['mode'] & {}>(\n\tconfig?: MySqlTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? MySqlTimestampStringBuilderInitial<''>\n\t: MySqlTimestampBuilderInitial<''>;\nexport function timestamp<TName extends string, TMode extends MySqlTimestampConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: MySqlTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? MySqlTimestampStringBuilderInitial<TName>\n\t: MySqlTimestampBuilderInitial<TName>;\nexport function timestamp(a?: string | MySqlTimestampConfig, b: MySqlTimestampConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlTimestampConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new MySqlTimestampStringBuilder(name, config);\n\t}\n\treturn new MySqlTimestampBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAmD;AACnD,yBAAgE;AAWzD,MAAM,8BACJ,8CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA0C;AACtE,UAAM,MAAM,QAAQ,gBAAgB;AACpC,SAAK,OAAO,MAAM,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,uCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,MAA0B,KAAK,OAAO;AAAA,EAE/C,aAAqB;AACpB,UAAM,YAAY,KAAK,QAAQ,SAAY,KAAK,IAAI,KAAK,GAAG;AAC5D,WAAO,YAAY,SAAS;AAAA,EAC7B;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,oBAAI,KAAK,QAAQ,OAAO;AAAA,EAChC;AAAA,EAES,iBAAiB,OAAqB;AAC9C,WAAO,MAAM,YAAY,EAAE,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,EACzD;AACD;AAWO,MAAM,oCACJ,8CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA0C;AACtE,UAAM,MAAM,UAAU,sBAAsB;AAC5C,SAAK,OAAO,MAAM,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACwD;AACxD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,6BACJ,uCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,MAA0B,KAAK,OAAO;AAAA,EAE/C,aAAqB;AACpB,UAAM,YAAY,KAAK,QAAQ,SAAY,KAAK,IAAI,KAAK,GAAG;AAC5D,WAAO,YAAY,SAAS;AAAA,EAC7B;AACD;AAmBO,SAAS,UAAU,GAAmC,IAA0B,CAAC,GAAG;AAC1F,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAyD,GAAG,CAAC;AACtF,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,4BAA4B,MAAM,MAAM;AAAA,EACpD;AACA,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}
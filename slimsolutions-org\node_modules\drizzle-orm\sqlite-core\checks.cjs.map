{"version": 3, "sources": ["../../src/sqlite-core/checks.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { SQLiteTable } from './table.ts';\n\nexport class CheckBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteCheckBuilder';\n\n\tprotected brand!: 'SQLiteConstraintBuilder';\n\n\tconstructor(public name: string, public value: SQL) {}\n\n\tbuild(table: SQLiteTable): Check {\n\t\treturn new Check(table, this);\n\t}\n}\n\nexport class Check {\n\tstatic readonly [entityKind]: string = 'SQLiteCheck';\n\n\tdeclare _: {\n\t\tbrand: 'SQLiteCheck';\n\t};\n\n\treadonly name: string;\n\treadonly value: SQL;\n\n\tconstructor(public table: SQLiteTable, builder: CheckBuilder) {\n\t\tthis.name = builder.name;\n\t\tthis.value = builder.value;\n\t}\n}\n\nexport function check(name: string, value: SQL): CheckBuilder {\n\treturn new CheckBuilder(name, value);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAIpB,MAAM,aAAa;AAAA,EAKzB,YAAmB,MAAqB,OAAY;AAAjC;AAAqB;AAAA,EAAa;AAAA,EAJrD,QAAiB,wBAAU,IAAY;AAAA,EAE7B;AAAA,EAIV,MAAM,OAA2B;AAChC,WAAO,IAAI,MAAM,OAAO,IAAI;AAAA,EAC7B;AACD;AAEO,MAAM,MAAM;AAAA,EAUlB,YAAmB,OAAoB,SAAuB;AAA3C;AAClB,SAAK,OAAO,QAAQ;AACpB,SAAK,QAAQ,QAAQ;AAAA,EACtB;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAM9B;AAAA,EACA;AAMV;AAEO,SAAS,MAAM,MAAc,OAA0B;AAC7D,SAAO,IAAI,aAAa,MAAM,KAAK;AACpC;", "names": []}
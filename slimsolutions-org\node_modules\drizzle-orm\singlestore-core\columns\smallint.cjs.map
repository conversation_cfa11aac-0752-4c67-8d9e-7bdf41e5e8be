{"version": 3, "sources": ["../../../src/singlestore-core/columns/smallint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\nimport type { SingleStoreIntConfig } from './int.ts';\n\nexport type SingleStoreSmallIntBuilderInitial<TName extends string> = SingleStoreSmallIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreSmallInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreSmallIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreSmallInt'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreSmallIntBuilder';\n\n\tconstructor(name: T['name'], config?: SingleStoreIntConfig) {\n\t\tsuper(name, 'number', 'SingleStoreSmallInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreSmallInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreSmallInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreSmallInt<T extends ColumnBaseConfig<'number', 'SingleStoreSmallInt'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreSmallInt';\n\n\tgetSQLType(): string {\n\t\treturn `smallint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function smallint(): SingleStoreSmallIntBuilderInitial<''>;\nexport function smallint(\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreSmallIntBuilderInitial<''>;\nexport function smallint<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreSmallIntBuilderInitial<TName>;\nexport function smallint(a?: string | SingleStoreIntConfig, b?: SingleStoreIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreIntConfig>(a, b);\n\treturn new SingleStoreSmallIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA8F;AAavF,MAAM,mCACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,qBAAqB;AAC3C,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,WAAW,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EAC1D;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAUO,SAAS,SAAS,GAAmC,GAA0B;AACrF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,2BAA2B,MAAM,MAAM;AACnD;", "names": []}
{"version": 3, "sources": ["../../src/vercel-postgres/session.ts"], "sourcesContent": ["import {\n\ttype QueryArrayConfig,\n\ttype QueryConfig,\n\ttype QueryResult,\n\ttype QueryResultRow,\n\ttypes,\n\ttype VercelClient,\n\tVercelPool,\n\ttype VercelPoolClient,\n} from '@vercel/postgres';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport { type PgDialect, PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport type VercelPgClient = VercelPool | VercelClient | VercelPoolClient;\n\nexport class VercelPgPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'VercelPgPreparedQuery';\n\n\tprivate rawQuery: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: VercelPgClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t\tthis.rawQuery = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// numeric[]\n\t\t\t\t\tif (typeId === 1231 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp[]\n\t\t\t\t\tif (typeId === 1115 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp with timezone[]\n\t\t\t\t\tif (typeId === 1185 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// interval[]\n\t\t\t\t\tif (typeId === 1187 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// date[]\n\t\t\t\t\tif (typeId === 1182 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// numeric[]\n\t\t\t\t\tif (typeId === 1231 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp[]\n\t\t\t\t\tif (typeId === 1115 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// timestamp with timezone[]\n\t\t\t\t\tif (typeId === 1185 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// interval[]\n\t\t\t\t\tif (typeId === 1187 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// date[]\n\t\t\t\t\tif (typeId === 1182 as any) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\n\t\tconst { fields, rawQuery, client, queryConfig: query, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn this.queryWithCache(rawQuery.text, params, async () => {\n\t\t\t\treturn await client.query(rawQuery, params);\n\t\t\t});\n\t\t}\n\n\t\tconst { rows } = await this.queryWithCache(query.text, params, async () => {\n\t\t\treturn await client.query(query, params);\n\t\t});\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\t\treturn this.queryWithCache(this.rawQuery.text, params, async () => {\n\t\t\treturn await this.client.query(this.rawQuery, params);\n\t\t}).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\t\treturn this.queryWithCache(this.queryConfig.text, params, async () => {\n\t\t\treturn await this.client.query(this.queryConfig, params);\n\t\t}).then((result) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface VercelPgSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class VercelPgSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<VercelPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'VercelPgSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: VercelPgClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: VercelPgSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T> {\n\t\treturn new VercelPgPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResult> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.query({\n\t\t\trowMode: 'array',\n\t\t\ttext: query,\n\t\t\tvalues: params,\n\t\t});\n\t\treturn result;\n\t}\n\n\tasync queryObjects<T extends QueryResultRow>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<QueryResult<T>> {\n\t\treturn this.client.query<T>(query, params);\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst result = await this.execute(sql);\n\n\t\treturn Number((result as any)['rows'][0]['count']);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: VercelPgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst session = this.client instanceof VercelPool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new VercelPgSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new VercelPgTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin${config ? sql` ${tx.getTransactionConfigSQL(config)}` : undefined}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof VercelPool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as VercelPoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class VercelPgTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<VercelPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'VercelPgTransaction';\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: VercelPgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new VercelPgTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface VercelPgQueryResultHKT extends PgQueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBASO;AAEP,mBAA0B;AAE1B,oBAA2B;AAC3B,oBAAwC;AACxC,qBAA8C;AAG9C,qBAA2C;AAE3C,iBAA4D;AAC5D,mBAA0C;AAInC,MAAM,8BAA6D,+BAAmB;AAAA,EAM5F,YACS,QACR,aACQ,QACA,QACR,OACA,eAIA,aACQ,QACR,MACQ,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAf7D;AAEA;AACA;AAOA;AAEA;AACA;AAGR,SAAK,WAAW;AAAA,MACf;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,sBAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,iBAAO,sBAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AACA,SAAK,cAAc;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,sBAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,sBAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,cAAI,WAAW,MAAa;AAC3B,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,iBAAO,sBAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EA7GA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EA4GR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAE/C,UAAM,EAAE,QAAQ,UAAU,QAAQ,aAAa,OAAO,qBAAqB,mBAAmB,IAAI;AAClG,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,KAAK,eAAe,SAAS,MAAM,QAAQ,YAAY;AAC7D,eAAO,MAAM,OAAO,MAAM,UAAU,MAAM;AAAA,MAC3C,CAAC;AAAA,IACF;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,eAAe,MAAM,MAAM,QAAQ,YAAY;AAC1E,aAAO,MAAM,OAAO,MAAM,OAAO,MAAM;AAAA,IACxC,CAAC;AAED,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAC/C,WAAO,KAAK,eAAe,KAAK,SAAS,MAAM,QAAQ,YAAY;AAClE,aAAO,MAAM,KAAK,OAAO,MAAM,KAAK,UAAU,MAAM;AAAA,IACrD,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChC;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAC/C,WAAO,KAAK,eAAe,KAAK,YAAY,MAAM,QAAQ,YAAY;AACrE,aAAO,MAAM,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM;AAAA,IACxD,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChC;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,wBAGH,yBAAwD;AAAA,EAMjE,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,uBAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAAyC;AACnE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,MAAM;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,aACL,OACA,QAC0B;AAC1B,WAAO,KAAK,OAAO,MAAS,OAAO,MAAM;AAAA,EAC1C;AAAA,EAEA,MAAe,MAAMA,MAA2B;AAC/C,UAAM,SAAS,MAAM,KAAK,QAAQA,IAAG;AAErC,WAAO,OAAQ,OAAe,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC;AAAA,EAClD;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,UAAU,KAAK,kBAAkB,6BACpC,IAAI,gBAAgB,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IACxF;AACH,UAAM,KAAK,IAAI,oBAA0C,KAAK,SAAS,SAAS,KAAK,MAAM;AAC3F,UAAM,GAAG,QAAQ,sBAAW,SAAS,kBAAO,GAAG,wBAAwB,MAAM,CAAC,KAAK,MAAS,EAAE;AAC9F,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,sBAAW;AAC5B,aAAO;AAAA,IACR,SAAS,OAAO;AACf,YAAM,GAAG,QAAQ,wBAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,KAAK,kBAAkB,4BAAY;AACtC,QAAC,QAAQ,OAA4B,QAAQ;AAAA,MAC9C;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,4BAGH,6BAA4D;AAAA,EACrE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["sql"]}
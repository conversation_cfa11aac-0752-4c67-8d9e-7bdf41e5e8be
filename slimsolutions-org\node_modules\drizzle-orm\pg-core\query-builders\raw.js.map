{"version": 3, "sources": ["../../../src/pg-core/query-builders/raw.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\n\nexport interface PgRaw<TResult> extends QueryPromise<TResult>, RunnableQuery<TResult, 'pg'>, SQLWrapper {}\n\nexport class PgRaw<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper, PreparedQuery\n{\n\tstatic override readonly [entityKind]: string = 'PgRaw';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tpublic execute: () => Promise<TResult>,\n\t\tprivate sql: SQL,\n\t\tprivate query: Query,\n\t\tprivate mapBatchResult: (result: unknown) => unknown,\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\tgetSQL() {\n\t\treturn this.sql;\n\t}\n\n\tgetQuery() {\n\t\treturn this.query;\n\t}\n\n\tmapResult(result: unknown, isFromBatch?: boolean) {\n\t\treturn isFromBatch ? this.mapBatchResult(result) : result;\n\t}\n\n\t_prepare(): PreparedQuery {\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn false;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAOtB,MAAM,cAAuB,aAEpC;AAAA,EAQC,YACQ,SACC,KACA,OACA,gBACP;AACD,UAAM;AALC;AACC;AACA;AACA;AAAA,EAGT;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA;AAAA,EAiBhD,SAAS;AACR,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,WAAW;AACV,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,UAAU,QAAiB,aAAuB;AACjD,WAAO,cAAc,KAAK,eAAe,MAAM,IAAI;AAAA,EACpD;AAAA,EAEA,WAA0B;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,wBAAwB;AACvB,WAAO;AAAA,EACR;AACD;", "names": []}
{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/resolve-rewrites.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport type { Rewrite } from '../../../../lib/load-custom-routes'\nimport { getPathMatch } from './path-match'\nimport { matchHas, prepareDestination } from './prepare-destination'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removeBasePath } from '../../../../client/remove-base-path'\nimport { parseRelativeUrl, type ParsedRelativeUrl } from './parse-relative-url'\n\ninterface ParsedAs extends Omit<ParsedRelativeUrl, 'slashes'> {\n  slashes: boolean | undefined\n}\n\nexport default function resolveRewrites(\n  asPath: string,\n  pages: string[],\n  rewrites: {\n    beforeFiles: Rewrite[]\n    afterFiles: Rewrite[]\n    fallback: Rewrite[]\n  },\n  query: ParsedUrlQuery,\n  resolveHref: (path: string) => string,\n  locales?: readonly string[]\n): {\n  matchedPage: boolean\n  parsedAs: ParsedAs\n  asPath: string\n  resolvedHref?: string\n  externalDest?: boolean\n} {\n  let matchedPage = false\n  let externalDest = false\n  let parsedAs: ParsedAs = parseRelativeUrl(asPath)\n  let fsPathname = removeTrailingSlash(\n    normalizeLocalePath(removeBasePath(parsedAs.pathname), locales).pathname\n  )\n  let resolvedHref\n\n  const handleRewrite = (rewrite: Rewrite) => {\n    const matcher = getPathMatch(\n      rewrite.source + (process.env.__NEXT_TRAILING_SLASH ? '(/)?' : ''),\n      {\n        removeUnnamedParams: true,\n        strict: true,\n      }\n    )\n\n    let params = matcher(parsedAs.pathname)\n\n    if ((rewrite.has || rewrite.missing) && params) {\n      const hasParams = matchHas(\n        {\n          headers: {\n            host: document.location.hostname,\n            'user-agent': navigator.userAgent,\n          },\n          cookies: document.cookie\n            .split('; ')\n            .reduce<Record<string, string>>((acc, item) => {\n              const [key, ...value] = item.split('=')\n              acc[key] = value.join('=')\n              return acc\n            }, {}),\n        } as any,\n        parsedAs.query,\n        rewrite.has,\n        rewrite.missing\n      )\n\n      if (hasParams) {\n        Object.assign(params, hasParams)\n      } else {\n        params = false\n      }\n    }\n\n    if (params) {\n      if (!rewrite.destination) {\n        // this is a proxied rewrite which isn't handled on the client\n        externalDest = true\n        return true\n      }\n      const destRes = prepareDestination({\n        appendParamsToQuery: true,\n        destination: rewrite.destination,\n        params: params,\n        query: query,\n      })\n      parsedAs = destRes.parsedDestination\n      asPath = destRes.newUrl\n      Object.assign(query, destRes.parsedDestination.query)\n\n      fsPathname = removeTrailingSlash(\n        normalizeLocalePath(removeBasePath(asPath), locales).pathname\n      )\n\n      if (pages.includes(fsPathname)) {\n        // check if we now match a page as this means we are done\n        // resolving the rewrites\n        matchedPage = true\n        resolvedHref = fsPathname\n        return true\n      }\n\n      // check if we match a dynamic-route, if so we break the rewrites chain\n      resolvedHref = resolveHref(fsPathname)\n\n      if (resolvedHref !== asPath && pages.includes(resolvedHref)) {\n        matchedPage = true\n        return true\n      }\n    }\n  }\n  let finished = false\n\n  for (let i = 0; i < rewrites.beforeFiles.length; i++) {\n    // we don't end after match in beforeFiles to allow\n    // continuing through all beforeFiles rewrites\n    handleRewrite(rewrites.beforeFiles[i])\n  }\n  matchedPage = pages.includes(fsPathname)\n\n  if (!matchedPage) {\n    if (!finished) {\n      for (let i = 0; i < rewrites.afterFiles.length; i++) {\n        if (handleRewrite(rewrites.afterFiles[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n\n    // check dynamic route before processing fallback rewrites\n    if (!finished) {\n      resolvedHref = resolveHref(fsPathname)\n      matchedPage = pages.includes(resolvedHref)\n      finished = matchedPage\n    }\n\n    if (!finished) {\n      for (let i = 0; i < rewrites.fallback.length; i++) {\n        if (handleRewrite(rewrites.fallback[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n  }\n\n  return {\n    asPath,\n    parsedAs,\n    matchedPage,\n    resolvedHref,\n    externalDest,\n  }\n}\n"], "names": ["getPathMatch", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeLocalePath", "removeBasePath", "parseRelativeUrl", "resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "externalDest", "parsedAs", "fsPathname", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "source", "process", "env", "__NEXT_TRAILING_SLASH", "removeUnnamedP<PERSON>ms", "strict", "params", "has", "missing", "hasParams", "headers", "host", "document", "location", "hostname", "navigator", "userAgent", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "appendParamsToQuery", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": "AAEA,SAASA,YAAY,QAAQ,eAAc;AAC3C,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,wBAAuB;AACpE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,gBAAgB,QAAgC,uBAAsB;AAM/E,eAAe,SAASC,gBACtBC,MAAc,EACdC,KAAe,EACfC,QAIC,EACDC,KAAqB,EACrBC,WAAqC,EACrCC,OAA2B;IAQ3B,IAAIC,cAAc;IAClB,IAAIC,eAAe;IACnB,IAAIC,WAAqBV,iBAAiBE;IAC1C,IAAIS,aAAad,oBACfC,oBAAoBC,eAAeW,SAASE,QAAQ,GAAGL,SAASK,QAAQ;IAE1E,IAAIC;IAEJ,MAAMC,gBAAgB,CAACC;QACrB,MAAMC,UAAUtB,aACdqB,QAAQE,MAAM,GAAIC,CAAAA,QAAQC,GAAG,CAACC,qBAAqB,GAAG,SAAS,EAAC,GAChE;YACEC,qBAAqB;YACrBC,QAAQ;QACV;QAGF,IAAIC,SAASP,QAAQN,SAASE,QAAQ;QAEtC,IAAI,AAACG,CAAAA,QAAQS,GAAG,IAAIT,QAAQU,OAAO,AAAD,KAAMF,QAAQ;YAC9C,MAAMG,YAAY/B,SAChB;gBACEgC,SAAS;oBACPC,MAAMC,SAASC,QAAQ,CAACC,QAAQ;oBAChC,cAAcC,UAAUC,SAAS;gBACnC;gBACAC,SAASL,SAASM,MAAM,CACrBC,KAAK,CAAC,MACNC,MAAM,CAAyB,CAACC,KAAKC;oBACpC,MAAM,CAACC,KAAK,GAAGC,MAAM,GAAGF,KAAKH,KAAK,CAAC;oBACnCE,GAAG,CAACE,IAAI,GAAGC,MAAMC,IAAI,CAAC;oBACtB,OAAOJ;gBACT,GAAG,CAAC;YACR,GACA5B,SAASL,KAAK,EACdU,QAAQS,GAAG,EACXT,QAAQU,OAAO;YAGjB,IAAIC,WAAW;gBACbiB,OAAOC,MAAM,CAACrB,QAAQG;YACxB,OAAO;gBACLH,SAAS;YACX;QACF;QAEA,IAAIA,QAAQ;YACV,IAAI,CAACR,QAAQ8B,WAAW,EAAE;gBACxB,8DAA8D;gBAC9DpC,eAAe;gBACf,OAAO;YACT;YACA,MAAMqC,UAAUlD,mBAAmB;gBACjCmD,qBAAqB;gBACrBF,aAAa9B,QAAQ8B,WAAW;gBAChCtB,QAAQA;gBACRlB,OAAOA;YACT;YACAK,WAAWoC,QAAQE,iBAAiB;YACpC9C,SAAS4C,QAAQG,MAAM;YACvBN,OAAOC,MAAM,CAACvC,OAAOyC,QAAQE,iBAAiB,CAAC3C,KAAK;YAEpDM,aAAad,oBACXC,oBAAoBC,eAAeG,SAASK,SAASK,QAAQ;YAG/D,IAAIT,MAAM+C,QAAQ,CAACvC,aAAa;gBAC9B,yDAAyD;gBACzD,yBAAyB;gBACzBH,cAAc;gBACdK,eAAeF;gBACf,OAAO;YACT;YAEA,uEAAuE;YACvEE,eAAeP,YAAYK;YAE3B,IAAIE,iBAAiBX,UAAUC,MAAM+C,QAAQ,CAACrC,eAAe;gBAC3DL,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,IAAI2C,WAAW;IAEf,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASiD,WAAW,CAACC,MAAM,EAAEF,IAAK;QACpD,mDAAmD;QACnD,8CAA8C;QAC9CtC,cAAcV,SAASiD,WAAW,CAACD,EAAE;IACvC;IACA5C,cAAcL,MAAM+C,QAAQ,CAACvC;IAE7B,IAAI,CAACH,aAAa;QAChB,IAAI,CAAC2C,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASmD,UAAU,CAACD,MAAM,EAAEF,IAAK;gBACnD,IAAItC,cAAcV,SAASmD,UAAU,CAACH,EAAE,GAAG;oBACzCD,WAAW;oBACX;gBACF;YACF;QACF;QAEA,0DAA0D;QAC1D,IAAI,CAACA,UAAU;YACbtC,eAAeP,YAAYK;YAC3BH,cAAcL,MAAM+C,QAAQ,CAACrC;YAC7BsC,WAAW3C;QACb;QAEA,IAAI,CAAC2C,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASoD,QAAQ,CAACF,MAAM,EAAEF,IAAK;gBACjD,IAAItC,cAAcV,SAASoD,QAAQ,CAACJ,EAAE,GAAG;oBACvCD,WAAW;oBACX;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACLjD;QACAQ;QACAF;QACAK;QACAJ;IACF;AACF", "ignoreList": [0]}
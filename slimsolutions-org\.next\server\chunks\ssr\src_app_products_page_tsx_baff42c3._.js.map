{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/slimsolutions/SlimSolutions_org/slimsolutions-org/src/app/products/page.tsx"], "sourcesContent": ["\"use client\";\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Clock, Star, Zap, Award, TrendingUp, Users, ArrowRight } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function ProductsPage() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Urgent Banner */}\n      <div className=\"bg-red-600 text-white py-3 px-4 text-center font-bold animate-pulse\">\n        🔥 LIMITED TIME: Get 50% OFF Our Best-Selling Weight Loss Program - Only 24 Hours Left!\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <Link href=\"/\" className=\"font-heading text-2xl font-bold text-primary\">\n              SlimSolutions\n            </Link>\n            <div className=\"hidden md:flex space-x-8\">\n              <Link href=\"/#features\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Features</Link>\n              <Link href=\"/#testimonials\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Success Stories</Link>\n              <Link href=\"/products\" className=\"text-primary font-semibold\">Products</Link>\n              <Link href=\"/#contact\" className=\"text-muted-foreground hover:text-foreground transition-colors\">Contact</Link>\n            </div>\n            <Link href=\"#order\" className=\"bg-red-600 text-white px-6 py-2 rounded-lg font-ui font-bold hover:bg-red-700 transition-colors\">\n              ORDER NOW\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"py-16 px-4 bg-gradient-to-br from-red-50 to-yellow-50\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h1 className=\"font-heading text-4xl lg:text-6xl font-bold text-gray-900 mb-6\">\n            <span className=\"text-red-600\">LOSE 10-30 LBS</span>\n            <span className=\"block\">In Just 30 Days</span>\n            <span className=\"text-primary block text-2xl lg:text-4xl\">Get The Complete System</span>\n          </h1>\n          <p className=\"text-xl text-gray-700 max-w-4xl mx-auto mb-8\">\n            Everything you need to transform your body and life - backed by our 60-day money-back guarantee\n          </p>\n          \n          <div className=\"bg-yellow-100 border-l-4 border-yellow-500 p-4 rounded max-w-2xl mx-auto\">\n            <div className=\"flex items-center justify-center\">\n              <Clock className=\"w-5 h-5 text-yellow-600 mr-2\" />\n              <p className=\"text-yellow-800 font-semibold\">\n                ⚠️ HURRY! Special pricing expires in: <span className=\"text-red-600 font-bold\">23:47:32</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Main Product Section */}\n      <section id=\"order\" className=\"py-16 px-4 bg-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n            {/* Product Details */}\n            <div className=\"space-y-8\">\n              <div>\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                  The Complete SlimSolutions System\n                </h2>\n                <p className=\"text-xl text-gray-700\">\n                  The exact system that helped 50,247 people lose stubborn belly fat in just 30 days\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">What's Included:</h3>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"flex items-start p-4 bg-green-50 rounded-lg border border-green-200\">\n                    <CheckCircle className=\"w-6 h-6 text-green-600 mr-4 mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-bold text-lg text-gray-900\">The Metabolic Reset Protocol</h4>\n                      <p className=\"text-gray-700\">Turn your body into a 24/7 fat-burning machine with our scientifically-proven method</p>\n                      <p className=\"text-green-700 font-semibold\">Value: $197</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                    <CheckCircle className=\"w-6 h-6 text-blue-600 mr-4 mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-bold text-lg text-gray-900\">Personalized Meal Plans</h4>\n                      <p className=\"text-gray-700\">Custom meal plans based on your food preferences and dietary restrictions</p>\n                      <p className=\"text-blue-700 font-semibold\">Value: $147</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start p-4 bg-purple-50 rounded-lg border border-purple-200\">\n                    <CheckCircle className=\"w-6 h-6 text-purple-600 mr-4 mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-bold text-lg text-gray-900\">15-Minute Fat-Burning Workouts</h4>\n                      <p className=\"text-gray-700\">No gym required! Simple exercises that fit into any schedule</p>\n                      <p className=\"text-purple-700 font-semibold\">Value: $97</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\n                    <CheckCircle className=\"w-6 h-6 text-yellow-600 mr-4 mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-bold text-lg text-gray-900\">24/7 Support Community</h4>\n                      <p className=\"text-gray-700\">Private Facebook group with 50,000+ supportive members</p>\n                      <p className=\"text-yellow-700 font-semibold\">Value: $97</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start p-4 bg-red-50 rounded-lg border border-red-200\">\n                    <CheckCircle className=\"w-6 h-6 text-red-600 mr-4 mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-bold text-lg text-gray-900\">Progress Tracking App</h4>\n                      <p className=\"text-gray-700\">Track your weight loss journey and stay motivated</p>\n                      <p className=\"text-red-700 font-semibold\">Value: $47</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-100 p-6 rounded-lg\">\n                  <div className=\"text-center\">\n                    <p className=\"text-gray-600 text-lg\">Total Value:</p>\n                    <p className=\"text-3xl font-bold text-gray-900 line-through\">$585</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Order Form */}\n            <div className=\"bg-white border-4 border-yellow-400 rounded-2xl p-8 shadow-2xl relative\">\n              <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-6 py-2 rounded-full font-bold text-sm\">\n                🎁 LIMITED TIME OFFER\n              </div>\n              \n              <div className=\"text-center mt-4 mb-8\">\n                <div className=\"text-gray-500 line-through text-2xl mb-2\">Regular Price: $585</div>\n                <div className=\"text-6xl font-bold text-red-600 mb-2\">$97</div>\n                <div className=\"text-green-600 font-bold text-xl mb-4\">Save $488 Today!</div>\n                <div className=\"text-red-600 font-bold text-lg\">83% OFF - Today Only!</div>\n              </div>\n\n              <button className=\"w-full bg-green-600 text-white px-8 py-6 rounded-lg font-bold text-2xl hover:bg-green-700 transition-all transform hover:scale-105 shadow-lg mb-6\">\n                🎁 GET INSTANT ACCESS NOW\n              </button>\n\n              <div className=\"space-y-3 text-center text-sm text-gray-600 mb-6\">\n                <div className=\"flex items-center justify-center\">\n                  <Shield className=\"w-4 h-4 mr-2 text-green-600\" />\n                  60-Day Money-Back Guarantee\n                </div>\n                <div className=\"flex items-center justify-center\">\n                  <Clock className=\"w-4 h-4 mr-2 text-blue-600\" />\n                  Instant Digital Access\n                </div>\n                <div className=\"flex items-center justify-center\">\n                  <CheckCircle className=\"w-4 h-4 mr-2 text-purple-600\" />\n                  No Monthly Fees - One-Time Payment\n                </div>\n                <div className=\"flex items-center justify-center\">\n                  <Users className=\"w-4 h-4 mr-2 text-yellow-600\" />\n                  Join 50,000+ Successful Members\n                </div>\n              </div>\n\n              <div className=\"bg-yellow-100 border border-yellow-300 rounded-lg p-4 text-center\">\n                <p className=\"text-yellow-800 font-semibold text-sm\">\n                  🔒 Secure Checkout • SSL Encrypted • 100% Safe\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Guarantee Section */}\n      <section className=\"py-16 px-4 bg-green-50\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg border-2 border-green-200\">\n            <Shield className=\"w-16 h-16 text-green-600 mx-auto mb-6\" />\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Our Iron-Clad 60-Day Guarantee\n            </h2>\n            <p className=\"text-xl text-gray-700 mb-6\">\n              Try the SlimSolutions System for a full 60 days. If you don't lose at least 10 pounds, \n              we'll refund every penny - no questions asked.\n            </p>\n            <p className=\"text-lg text-green-700 font-semibold\">\n              That's how confident we are that this system will work for you!\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-16 px-4 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            Frequently Asked Questions\n          </h2>\n          \n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                How quickly will I see results?\n              </h3>\n              <p className=\"text-gray-700\">\n                Most people see noticeable results within the first 7-10 days. Our average member loses \n                10-15 pounds in the first month, with some losing up to 30 pounds.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Do I need to exercise for hours every day?\n              </h3>\n              <p className=\"text-gray-700\">\n                Not at all! Our workouts are just 15 minutes and can be done at home with no equipment. \n                The focus is on efficiency, not duration.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                What if I have dietary restrictions?\n              </h3>\n              <p className=\"text-gray-700\">\n                Our meal plans are completely customizable. Whether you're vegetarian, vegan, keto, \n                or have food allergies, we can create a plan that works for you.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Is this a monthly subscription?\n              </h3>\n              <p className=\"text-gray-700\">\n                No! This is a one-time payment of $97. You get lifetime access to all materials \n                and future updates at no additional cost.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Final CTA */}\n      <section className=\"py-16 px-4 bg-red-600 text-white\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-4xl font-bold mb-6\">\n            Don't Wait Another Day to Start Your Transformation\n          </h2>\n          <p className=\"text-xl mb-8\">\n            Every day you wait is another day you could be losing weight and feeling amazing. \n            Join the 50,000+ people who have already transformed their lives.\n          </p>\n          \n          <button className=\"bg-yellow-400 text-black px-12 py-6 rounded-lg font-bold text-2xl hover:bg-yellow-300 transition-all transform hover:scale-105 shadow-lg\">\n            🎁 CLAIM YOUR TRANSFORMATION NOW\n          </button>\n          \n          <p className=\"text-red-100 text-sm mt-6\">\n            60-Day Money-Back Guarantee • Instant Access • One-Time Payment\n          </p>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <div className=\"font-heading text-2xl font-bold mb-4\">SlimSolutions</div>\n          <p className=\"text-gray-400 mb-6\">\n            Transforming lives through proven weight loss solutions\n          </p>\n          <div className=\"flex justify-center space-x-8 text-sm text-gray-400\">\n            <Link href=\"/privacy\" className=\"hover:text-white transition-colors\">Privacy Policy</Link>\n            <Link href=\"/terms\" className=\"hover:text-white transition-colors\">Terms of Service</Link>\n            <Link href=\"/contact\" className=\"hover:text-white transition-colors\">Contact</Link>\n          </div>\n          <p className=\"text-gray-500 text-xs mt-6\">\n            © 2024 SlimSolutions. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BAAsE;;;;;;0BAKrF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA+C;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAgE;;;;;;kDAClG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAgE;;;;;;kDACtG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA6B;;;;;;kDAC9D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAgE;;;;;;;;;;;;0CAEnG,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAkG;;;;;;;;;;;;;;;;;;;;;;0BAQtI,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAA0C;;;;;;;;;;;;sCAE5D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;;4CAAgC;0DACL,8OAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzF,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;kFAC7B,8OAAC;wEAAE,WAAU;kFAA+B;;;;;;;;;;;;;;;;;;kEAIhD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;kFAC7B,8OAAC;wEAAE,WAAU;kFAA8B;;;;;;;;;;;;;;;;;;kEAI/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;kFAC7B,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAIjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;kFAC7B,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAIjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;kFAC7B,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;;;;;;;0DAKhD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqH;;;;;;kDAIpI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAiC;;;;;;;;;;;;kDAGlD,8OAAC;wCAAO,WAAU;kDAAoJ;;;;;;kDAItK,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;0DAGpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAG1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;;kDAKtD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAK5B,8OAAC;4BAAO,WAAU;sCAA2I;;;;;;sCAI7J,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAqC;;;;;;8CACrE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAqC;;;;;;8CACnE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAqC;;;;;;;;;;;;sCAEvE,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}]}
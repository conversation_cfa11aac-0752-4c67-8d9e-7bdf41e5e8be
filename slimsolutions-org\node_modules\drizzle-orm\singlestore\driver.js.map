{"version": 3, "sources": ["../../src/singlestore/driver.ts"], "sourcesContent": ["import { type Connection as CallbackConnection, createPool, type Pool as CallbackPool, type PoolOptions } from 'mysql2';\nimport type { Connection, Pool } from 'mysql2/promise';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { SingleStoreDatabase } from '~/singlestore-core/db.ts';\nimport { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { npmVersion } from '~/version.ts';\nimport type {\n\tSingleStoreDriverClient,\n\tSingleStoreDriverPreparedQueryHKT,\n\tSingleStoreDriverQueryResultHKT,\n} from './session.ts';\nimport { SingleStoreDriverSession } from './session.ts';\n\nexport interface SingleStoreDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class SingleStoreDriverDriver {\n\tstatic readonly [entityKind]: string = 'SingleStoreDriverDriver';\n\n\tconstructor(\n\t\tprivate client: SingleStoreDriverClient,\n\t\tprivate dialect: SingleStoreDialect,\n\t\tprivate options: SingleStoreDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): SingleStoreDriverSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new SingleStoreDriverSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t\tcache: this.options.cache,\n\t\t});\n\t}\n}\n\nexport { SingleStoreDatabase } from '~/singlestore-core/db.ts';\n\nexport class SingleStoreDriverDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends SingleStoreDatabase<SingleStoreDriverQueryResultHKT, SingleStoreDriverPreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDriverDatabase';\n}\n\nexport type SingleStoreDriverDrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> =\n\t& Omit<DrizzleConfig<TSchema>, 'schema'>\n\t& ({ schema: TSchema } | { schema?: undefined });\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Pool | Connection | CallbackPool | CallbackConnection = CallbackPool,\n>(\n\tclient: TClient,\n\tconfig: SingleStoreDriverDrizzleConfig<TSchema> = {},\n): SingleStoreDriverDatabase<TSchema> & {\n\t$client: AnySingleStoreDriverConnection extends TClient ? CallbackPool : TClient;\n} {\n\tconst dialect = new SingleStoreDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tconst clientForInstance = isCallbackClient(client) ? client.promise() : client;\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new SingleStoreDriverDriver(clientForInstance as SingleStoreDriverClient, dialect, {\n\t\tlogger,\n\t\tcache: config.cache,\n\t});\n\tconst session = driver.createSession(schema);\n\tconst db = new SingleStoreDriverDatabase(dialect, session, schema as any) as SingleStoreDriverDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\ninterface CallbackClient {\n\tpromise(): SingleStoreDriverClient;\n}\n\nfunction isCallbackClient(client: any): client is CallbackClient {\n\treturn typeof client.promise === 'function';\n}\n\nexport type AnySingleStoreDriverConnection = Pool | Connection | CallbackPool | CallbackConnection;\n\nconst CONNECTION_ATTRS: PoolOptions['connectAttributes'] = {\n\t_connector_name: 'SingleStore Drizzle ORM Driver',\n\t_connector_version: npmVersion,\n};\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends AnySingleStoreDriverConnection = CallbackPool,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tSingleStoreDriverDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& SingleStoreDriverDrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | PoolOptions;\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): SingleStoreDriverDatabase<TSchema> & {\n\t$client: AnySingleStoreDriverConnection extends TClient ? CallbackPool : TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst connectionString = params[0]!;\n\t\tconst instance = createPool({\n\t\t\turi: connectionString,\n\t\t\tconnectAttributes: CONNECTION_ATTRS,\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& { connection?: PoolOptions | string; client?: TClient }\n\t\t\t& SingleStoreDriverDrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tlet opts: PoolOptions = {};\n\t\topts = typeof connection === 'string'\n\t\t\t? {\n\t\t\t\turi: connection,\n\t\t\t\tsupportBigNumbers: true,\n\t\t\t\tconnectAttributes: CONNECTION_ATTRS,\n\t\t\t}\n\t\t\t: {\n\t\t\t\t...connection,\n\t\t\t\tconnectAttributes: {\n\t\t\t\t\t...connection!.connectAttributes,\n\t\t\t\t\t...CONNECTION_ATTRS,\n\t\t\t\t},\n\t\t\t};\n\n\t\tconst instance = createPool(opts);\n\t\tconst db = construct(instance, drizzleConfig);\n\n\t\treturn db as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as SingleStoreDriverDrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: SingleStoreDriverDrizzleConfig<TSchema>,\n\t): SingleStoreDriverDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,SAAgD,kBAA+D;AAG/G,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,2BAA2B;AACpC,SAAS,0BAA0B;AACnC,SAA6B,gBAAgB;AAC7C,SAAS,kBAAkB;AAM3B,SAAS,gCAAgC;AAOlC,MAAM,wBAAwB;AAAA,EAGpC,YACS,QACA,SACA,UAAoC,CAAC,GAC5C;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,UAAU,IAAY;AAAA,EASvC,cACC,QAC4E;AAC5E,WAAO,IAAI,yBAAyB,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MACtE,QAAQ,KAAK,QAAQ;AAAA,MACrB,OAAO,KAAK,QAAQ;AAAA,IACrB,CAAC;AAAA,EACF;AACD;AAEA,SAAS,uBAAAA,4BAA2B;AAE7B,MAAM,kCAEH,oBAAiG;AAAA,EAC1G,QAA0B,UAAU,IAAY;AACjD;AAMA,SAAS,UAIR,QACA,SAAkD,CAAC,GAGlD;AACD,QAAM,UAAU,IAAI,mBAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,QAAM,oBAAoB,iBAAiB,MAAM,IAAI,OAAO,QAAQ,IAAI;AAExE,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,wBAAwB,mBAA8C,SAAS;AAAA,IACjG;AAAA,IACA,OAAO,OAAO;AAAA,EACf,CAAC;AACD,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,QAAM,KAAK,IAAI,0BAA0B,SAAS,SAAS,MAAa;AACxE,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAMA,SAAS,iBAAiB,QAAuC;AAChE,SAAO,OAAO,OAAO,YAAY;AAClC;AAIA,MAAM,mBAAqD;AAAA,EAC1D,iBAAiB;AAAA,EACjB,oBAAoB;AACrB;AAEO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,mBAAmB,OAAO,CAAC;AACjC,UAAM,WAAW,WAAW;AAAA,MAC3B,KAAK;AAAA,MACL,mBAAmB;AAAA,IACpB,CAAC;AAED,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAIzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAoB,CAAC;AACzB,WAAO,OAAO,eAAe,WAC1B;AAAA,MACD,KAAK;AAAA,MACL,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACpB,IACE;AAAA,MACD,GAAG;AAAA,MACH,mBAAmB;AAAA,QAClB,GAAG,WAAY;AAAA,QACf,GAAG;AAAA,MACJ;AAAA,IACD;AAED,UAAM,WAAW,WAAW,IAAI;AAChC,UAAM,KAAK,UAAU,UAAU,aAAa;AAE5C,WAAO;AAAA,EACR;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAwD;AACxG;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["SingleStoreDatabase", "drizzle"]}
{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { SQLiteDialect } from '~/sqlite-core/dialect.ts';\nimport type { IndexColumn } from '~/sqlite-core/indexes.ts';\nimport type { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { SQLiteTable } from '~/sqlite-core/table.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Columns, Table } from '~/table.ts';\nimport { type DrizzleTypeError, haveSame<PERSON>eys, mapUpdateSet, orderSelectedFields, type Simplify } from '~/utils.ts';\nimport type { AnySQLiteColumn, SQLiteColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport { QueryBuilder } from './query-builder.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { SQLiteUpdateSetSource } from './update.ts';\n\nexport interface SQLiteInsertConfig<TTable extends SQLiteTable = SQLiteTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[] | SQLiteInsertSelectQueryBuilder<TTable> | SQL;\n\twithList?: Subquery[];\n\tonConflict?: SQL[];\n\treturning?: SelectedFieldsOrdered;\n\tselect?: boolean;\n}\n\nexport type SQLiteInsertValue<TTable extends SQLiteTable> = Simplify<\n\t{\n\t\t[Key in keyof TTable['$inferInsert']]: TTable['$inferInsert'][Key] | SQL | Placeholder;\n\t}\n>;\n\nexport type SQLiteInsertSelectQueryBuilder<TTable extends SQLiteTable> = TypedQueryBuilder<\n\t{ [K in keyof TTable['$inferInsert']]: AnySQLiteColumn | SQL | SQL.Aliased | TTable['$inferInsert'][K] }\n>;\n\nexport class SQLiteInsertBuilder<\n\tTTable extends SQLiteTable,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteInsertBuilder';\n\n\tconstructor(\n\t\tprotected table: TTable,\n\t\tprotected session: SQLiteSession<any, any, any, any>,\n\t\tprotected dialect: SQLiteDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tvalues(value: SQLiteInsertValue<TTable>): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tvalues(values: SQLiteInsertValue<TTable>[]): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tvalues(\n\t\tvalues: SQLiteInsertValue<TTable> | SQLiteInsertValue<TTable>[],\n\t): SQLiteInsertBase<TTable, TResultType, TRunResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\t// if (mappedValues.length > 1 && mappedValues.some((t) => Object.keys(t).length === 0)) {\n\t\t// \tthrow new Error(\n\t\t// \t\t`One of the values you want to insert is empty. In SQLite you can insert only one empty object per statement. For this case Drizzle with use \"INSERT INTO ... DEFAULT VALUES\" syntax`,\n\t\t// \t);\n\t\t// }\n\n\t\treturn new SQLiteInsertBase(this.table, mappedValues, this.session, this.dialect, this.withList);\n\t}\n\n\tselect(\n\t\tselectQuery: (qb: QueryBuilder) => SQLiteInsertSelectQueryBuilder<TTable>,\n\t): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tselect(selectQuery: (qb: QueryBuilder) => SQL): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tselect(selectQuery: SQL): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tselect(selectQuery: SQLiteInsertSelectQueryBuilder<TTable>): SQLiteInsertBase<TTable, TResultType, TRunResult>;\n\tselect(\n\t\tselectQuery:\n\t\t\t| SQL\n\t\t\t| SQLiteInsertSelectQueryBuilder<TTable>\n\t\t\t| ((qb: QueryBuilder) => SQLiteInsertSelectQueryBuilder<TTable> | SQL),\n\t): SQLiteInsertBase<TTable, TResultType, TRunResult> {\n\t\tconst select = typeof selectQuery === 'function' ? selectQuery(new QueryBuilder()) : selectQuery;\n\n\t\tif (\n\t\t\t!is(select, SQL)\n\t\t\t&& !haveSameKeys(this.table[Columns], select._.selectedFields)\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t'Insert select error: selected fields are not the same or are in a different order compared to the table definition',\n\t\t\t);\n\t\t}\n\n\t\treturn new SQLiteInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n\t}\n}\n\nexport type SQLiteInsertWithout<T extends AnySQLiteInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tSQLiteInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['resultType'],\n\t\t\t\tT['_']['runResult'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type SQLiteInsertReturning<\n\tT extends AnySQLiteInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = SQLiteInsertWithout<\n\tSQLiteInsertBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteInsertReturningAll<\n\tT extends AnySQLiteInsert,\n\tTDynamic extends boolean,\n> = SQLiteInsertWithout<\n\tSQLiteInsertBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteInsertOnConflictDoUpdateConfig<T extends AnySQLiteInsert> = {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated - use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: SQLiteUpdateSetSource<T['_']['table']>;\n};\n\nexport type SQLiteInsertDynamic<T extends AnySQLiteInsert> = SQLiteInsert<\n\tT['_']['table'],\n\tT['_']['resultType'],\n\tT['_']['runResult'],\n\tT['_']['returning']\n>;\n\nexport type SQLiteInsertExecute<T extends AnySQLiteInsert> = T['_']['returning'] extends undefined ? T['_']['runResult']\n\t: T['_']['returning'][];\n\nexport type SQLiteInsertPrepare<T extends AnySQLiteInsert> = SQLitePreparedQuery<\n\t{\n\t\ttype: T['_']['resultType'];\n\t\trun: T['_']['runResult'];\n\t\tall: T['_']['returning'] extends undefined ? DrizzleTypeError<'.all() cannot be used without .returning()'>\n\t\t\t: T['_']['returning'][];\n\t\tget: T['_']['returning'] extends undefined ? DrizzleTypeError<'.get() cannot be used without .returning()'>\n\t\t\t: T['_']['returning'];\n\t\tvalues: T['_']['returning'] extends undefined ? DrizzleTypeError<'.values() cannot be used without .returning()'>\n\t\t\t: any[][];\n\t\texecute: SQLiteInsertExecute<T>;\n\t}\n>;\n\nexport type AnySQLiteInsert = SQLiteInsertBase<any, any, any, any, any, any>;\n\nexport type SQLiteInsert<\n\tTTable extends SQLiteTable = SQLiteTable,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTReturning = any,\n> = SQLiteInsertBase<TTable, TResultType, TRunResult, TReturning, true, never>;\n\nexport interface SQLiteInsertBase<\n\tTTable extends SQLiteTable,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTReturning = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tSQLWrapper,\n\tQueryPromise<TReturning extends undefined ? TRunResult : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? TRunResult : TReturning[], 'sqlite'>\n{\n\treadonly _: {\n\t\treadonly dialect: 'sqlite';\n\t\treadonly table: TTable;\n\t\treadonly resultType: TResultType;\n\t\treadonly runResult: TRunResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? TRunResult : TReturning[];\n\t};\n}\n\nexport class SQLiteInsertBase<\n\tTTable extends SQLiteTable,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTReturning = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? TRunResult : TReturning[]>\n\timplements RunnableQuery<TReturning extends undefined ? TRunResult : TReturning[], 'sqlite'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteInsert';\n\n\t/** @internal */\n\tconfig: SQLiteInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: SQLiteInsertConfig['values'],\n\t\tprivate session: SQLiteSession<any, any, any, any>,\n\t\tprivate dialect: SQLiteDialect,\n\t\twithList?: Subquery[],\n\t\tselect?: boolean,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values: values as any, withList, select };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): SQLiteInsertReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): SQLiteInsertReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[SQLiteTable.Symbol.Columns],\n\t): SQLiteInsertWithout<AnySQLiteInsert, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<SQLiteColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(config: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {}): this {\n\t\tif (!this.config.onConflict) this.config.onConflict = [];\n\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict.push(sql` on conflict do nothing`);\n\t\t} else {\n\t\t\tconst targetSql = Array.isArray(config.target) ? sql`${config.target}` : sql`${[config.target]}`;\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : sql``;\n\t\t\tthis.config.onConflict.push(sql` on conflict ${targetSql} do nothing${whereSql}`);\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     where: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(config: SQLiteInsertOnConflictDoUpdateConfig<this>): this {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\n\t\tif (!this.config.onConflict) this.config.onConflict = [];\n\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst targetSql = Array.isArray(config.target) ? sql`${config.target}` : sql`${[config.target]}`;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tthis.config.onConflict.push(\n\t\t\tsql` on conflict ${targetSql}${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`,\n\t\t);\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(isOneTimeQuery = true): SQLiteInsertPrepare<this> {\n\t\treturn this.session[isOneTimeQuery ? 'prepareOneTimeQuery' : 'prepareQuery'](\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t\tthis.config.returning ? 'all' : 'run',\n\t\t\ttrue,\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\ttype: 'insert',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t},\n\t\t) as SQLiteInsertPrepare<this>;\n\t}\n\n\tprepare(): SQLiteInsertPrepare<this> {\n\t\treturn this._prepare(false);\n\t}\n\n\trun: ReturnType<this['prepare']>['run'] = (placeholderValues) => {\n\t\treturn this._prepare().run(placeholderValues);\n\t};\n\n\tall: ReturnType<this['prepare']>['all'] = (placeholderValues) => {\n\t\treturn this._prepare().all(placeholderValues);\n\t};\n\n\tget: ReturnType<this['prepare']>['get'] = (placeholderValues) => {\n\t\treturn this._prepare().get(placeholderValues);\n\t};\n\n\tvalues: ReturnType<this['prepare']>['values'] = (placeholderValues) => {\n\t\treturn this._prepare().values(placeholderValues);\n\t};\n\n\toverride async execute(): Promise<SQLiteInsertExecute<this>> {\n\t\treturn (this.config.returning ? this.all() : this.run()) as SQLiteInsertExecute<this>;\n\t}\n\n\t$dynamic(): SQLiteInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAG/B,SAAS,oBAAoB;AAG7B,SAAS,OAAO,KAAK,WAAW;AAIhC,SAAS,mBAAmB;AAE5B,SAAS,SAAS,aAAa;AAC/B,SAAgC,cAAc,cAAc,2BAA0C;AAEtG,SAAS,wBAAwB;AACjC,SAAS,oBAAoB;AAuBtB,MAAM,oBAIX;AAAA,EAGD,YACW,OACA,SACA,SACF,UACP;AAJS;AACA;AACA;AACF;AAAA,EACN;AAAA,EAPH,QAAiB,UAAU,IAAY;AAAA,EAWvC,OACC,QACoD;AACpD,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAQD,WAAO,IAAI,iBAAiB,KAAK,OAAO,cAAc,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ;AAAA,EAChG;AAAA,EAQA,OACC,aAIoD;AACpD,UAAM,SAAS,OAAO,gBAAgB,aAAa,YAAY,IAAI,aAAa,CAAC,IAAI;AAErF,QACC,CAAC,GAAG,QAAQ,GAAG,KACZ,CAAC,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,cAAc,GAC5D;AACD,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,WAAO,IAAI,iBAAiB,KAAK,OAAO,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI;AAAA,EAChG;AACD;AAoHO,MAAM,yBAUH,aAEV;AAAA,EAMC,YACC,OACA,QACQ,SACA,SACR,UACA,QACC;AACD,UAAM;AALE;AACA;AAKR,SAAK,SAAS,EAAE,OAAO,QAAuB,UAAU,OAAO;AAAA,EAChE;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA;AAAA,EAGhD;AAAA,EAsCA,UACC,SAA6B,KAAK,OAAO,MAAM,YAAY,OAAO,OAAO,GACX;AAC9D,SAAK,OAAO,YAAY,oBAAkC,MAAM;AAChE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,oBAAoB,SAAgE,CAAC,GAAS;AAC7F,QAAI,CAAC,KAAK,OAAO,WAAY,MAAK,OAAO,aAAa,CAAC;AAEvD,QAAI,OAAO,WAAW,QAAW;AAChC,WAAK,OAAO,WAAW,KAAK,4BAA4B;AAAA,IACzD,OAAO;AACN,YAAM,YAAY,MAAM,QAAQ,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,KAAK,MAAM,CAAC,OAAO,MAAM,CAAC;AAC9F,YAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,WAAK,OAAO,WAAW,KAAK,mBAAmB,SAAS,cAAc,QAAQ,EAAE;AAAA,IACjF;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,mBAAmB,QAA0D;AAC5E,QAAI,OAAO,UAAU,OAAO,eAAe,OAAO,WAAW;AAC5D,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,QAAI,CAAC,KAAK,OAAO,WAAY,MAAK,OAAO,aAAa,CAAC;AAEvD,UAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,UAAM,iBAAiB,OAAO,cAAc,aAAa,OAAO,WAAW,KAAK;AAChF,UAAM,cAAc,OAAO,WAAW,aAAa,OAAO,QAAQ,KAAK;AACvE,UAAM,YAAY,MAAM,QAAQ,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,KAAK,MAAM,CAAC,OAAO,MAAM,CAAC;AAC9F,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,SAAK,OAAO,WAAW;AAAA,MACtB,mBAAmB,SAAS,GAAG,cAAc,kBAAkB,MAAM,GAAG,QAAQ,GAAG,WAAW;AAAA,IAC/F;AACA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,iBAAiB,MAAiC;AAC1D,WAAO,KAAK,QAAQ,iBAAiB,wBAAwB,cAAc;AAAA,MAC1E,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO,YAAY,QAAQ;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,QAAQ,iBAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAAA,EAEA,UAAqC;AACpC,WAAO,KAAK,SAAS,KAAK;AAAA,EAC3B;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,SAAgD,CAAC,sBAAsB;AACtE,WAAO,KAAK,SAAS,EAAE,OAAO,iBAAiB;AAAA,EAChD;AAAA,EAEA,MAAe,UAA8C;AAC5D,WAAQ,KAAK,OAAO,YAAY,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,EACvD;AAAA,EAEA,WAAsC;AACrC,WAAO;AAAA,EACR;AACD;", "names": []}
import { Heart, Target, Users } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-accent/5 py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="font-heading text-h1 text-foreground mb-6 animate-fade-in">
            Welcome to <span className="text-primary">SlimSolutions</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed animate-slide-up">
            Your trusted guide to a healthy lifestyle. Discover effective weight loss strategies,
            healthy recipes, and practical tips for sustainable weight management.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
            <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-ui font-medium hover:bg-primary/90 transition-colors">
              Start Your Journey
            </button>
            <button className="border border-border text-foreground px-8 py-3 rounded-lg font-ui font-medium hover:bg-secondary transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="font-heading text-h2 text-center text-foreground mb-12">
            Why Choose SlimSolutions?
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-background rounded-lg shadow-sm border border-border">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-heading text-h3 text-foreground mb-3">Targeted Approach</h3>
              <p className="text-muted-foreground">
                Personalized strategies that align with your lifestyle and goals.
              </p>
            </div>

            <div className="text-center p-6 bg-background rounded-lg shadow-sm border border-border">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-accent" />
              </div>
              <h3 className="font-heading text-h3 text-foreground mb-3">Healthy Choices</h3>
              <p className="text-muted-foreground">
                Proven methods for sustainable weight loss and a healthier lifestyle.
              </p>
            </div>

            <div className="text-center p-6 bg-background rounded-lg shadow-sm border border-border">
              <div className="w-16 h-16 bg-secondary/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-secondary-foreground" />
              </div>
              <h3 className="font-heading text-h3 text-foreground mb-3">Community Support</h3>
              <p className="text-muted-foreground">
                Share your journey with like-minded people and get the support you need.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-primary/5">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="font-heading text-h2 text-foreground mb-6">
            Ready to Start Your Transformation?
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Join thousands of people who have already achieved their goals with SlimSolutions.
          </p>
          <button className="bg-primary text-primary-foreground px-10 py-4 rounded-lg font-ui font-semibold text-lg hover:bg-primary/90 transition-colors">
            Get Started Now
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-secondary py-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h3 className="font-heading text-xl text-foreground mb-4">SlimSolutions</h3>
          <p className="text-muted-foreground mb-6">
            Your partner for a healthy and sustainable lifestyle.
          </p>
          <div className="text-sm text-muted-foreground">
            © 2024 SlimSolutions. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}

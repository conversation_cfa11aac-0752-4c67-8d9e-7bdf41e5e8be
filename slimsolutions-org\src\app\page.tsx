"use client";

import { Heart, Target, Users, Star, TrendingUp, Shield, CheckCircle, ArrowRight, Play, Clock, Award, Zap } from "lucide-react";
import { useState } from "react";
import Link from "next/link";

export default function Home() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          source: 'homepage_hero',
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('🎉 SUCCESS! Check your email for your FREE Fat-Burning Blueprint!');
        setEmail('');
      } else {
        setMessage('❌ ' + (data.error || 'Something went wrong. Please try again.'));
      }
    } catch (error) {
      setMessage('❌ Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="min-h-screen bg-background">
      {/* Urgent Banner */}
      <div className="bg-red-600 text-white py-3 px-4 text-center font-bold animate-pulse">
        🔥 LIMITED TIME: Get 50% OFF Our Best-Selling Weight Loss Program - Only 24 Hours Left!
      </div>

      {/* Navigation */}
      <nav className="bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="font-heading text-2xl font-bold text-primary">
              SlimSolutions
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="text-muted-foreground hover:text-foreground transition-colors">Features</a>
              <a href="#testimonials" className="text-muted-foreground hover:text-foreground transition-colors">Success Stories</a>
              <Link href="/products" className="text-primary font-semibold hover:text-primary/80 transition-colors">Products</Link>
              <a href="#contact" className="text-muted-foreground hover:text-foreground transition-colors">Contact</a>
            </div>
            <Link href="/products" className="bg-red-600 text-white px-6 py-2 rounded-lg font-ui font-bold hover:bg-red-700 transition-colors">
              GET ACCESS NOW
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-red-50 via-background to-yellow-50 py-16 px-4">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-bold animate-pulse">
                  <Award className="w-4 h-4 mr-2" />
                  #1 PROVEN Weight Loss System
                </div>
                <h1 className="font-heading text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  <span className="text-red-600">LOSE 10-30 LBS</span>
                  <span className="block">In Just 30 Days</span>
                  <span className="text-primary block text-2xl lg:text-4xl">WITHOUT Starving Yourself!</span>
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed font-medium">
                  <strong className="text-red-600">BREAKTHROUGH:</strong> Discover the secret "Fat-Burning Switch" that helped
                  <span className="text-primary font-bold"> 50,247 people</span> lose stubborn belly fat
                  in just 30 days - even if you've tried everything before!
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/products" className="bg-red-600 text-white px-8 py-4 rounded-lg font-ui font-bold text-lg hover:bg-red-700 transition-all transform hover:scale-105 flex items-center justify-center shadow-lg">
                  <Zap className="w-5 h-5 mr-2" />
                  GET INSTANT ACCESS NOW
                </Link>
                <button className="border-2 border-primary text-primary px-8 py-4 rounded-lg font-ui font-semibold text-lg hover:bg-primary hover:text-white transition-colors flex items-center justify-center">
                  <Play className="w-5 h-5 mr-2" />
                  Watch Transformation Video
                </button>
              </div>

              {/* Urgency Timer */}
              <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4 rounded">
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-yellow-600 mr-2" />
                  <p className="text-yellow-800 font-semibold">
                    ⚠️ HURRY! Special pricing expires in: <span className="text-red-600 font-bold">23:47:32</span>
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-8 pt-4">
                <div className="text-center">
                  <div className="font-heading text-3xl font-bold text-green-600">50,247</div>
                  <div className="text-sm text-muted-foreground font-medium">People Transformed</div>
                </div>
                <div className="text-center">
                  <div className="font-heading text-3xl font-bold text-green-600">97.3%</div>
                  <div className="text-sm text-muted-foreground font-medium">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="font-heading text-3xl font-bold text-green-600">18.5 lbs</div>
                  <div className="text-sm text-muted-foreground font-medium">Average Loss</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border-4 border-yellow-400 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-6 py-2 rounded-full font-bold text-sm">
                  🎁 FREE GIFT INSIDE
                </div>

                <div className="text-center mb-6 mt-4">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    Get Your FREE Fat-Burning Blueprint
                  </h3>
                  <p className="text-gray-600 font-medium">
                    The exact system that helped Sarah lose 32 lbs in 30 days!
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email for INSTANT access"
                      className="w-full px-4 py-4 rounded-lg border-2 border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary text-gray-900 placeholder-gray-500 font-medium"
                      required
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-green-600 text-white px-6 py-4 rounded-lg font-bold text-lg hover:bg-green-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    {isSubmitting ? 'SENDING YOUR BLUEPRINT...' : '🎁 SEND ME MY FREE BLUEPRINT NOW!'}
                  </button>
                </form>

                {message && (
                  <div className={`mt-4 p-4 rounded-lg text-center font-medium ${
                    message.includes('🎉')
                      ? 'bg-green-100 text-green-800 border-2 border-green-200'
                      : 'bg-red-100 text-red-800 border-2 border-red-200'
                  }`}>
                    {message}
                  </div>
                )}

                <div className="mt-4 space-y-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Instant download - no waiting
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    100% free - no credit card required
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Join 50,000+ successful members
                  </div>
                </div>

                <p className="text-xs text-gray-500 text-center mt-4">
                  🔒 Your email is 100% secure. No spam, ever.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem/Solution Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-heading text-4xl font-bold text-gray-900 mb-6">
              Are You TIRED of Trying Diet After Diet...
              <span className="text-red-600 block">Only to Gain the Weight Back?</span>
            </h2>
            <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              If you've tried "everything" but still struggle with stubborn belly fat, it's NOT your fault.
              The problem is you've been following outdated advice that actually <strong>slows down</strong> your metabolism!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">Here's What's REALLY Keeping You Overweight:</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-red-600 font-bold text-sm">✗</span>
                  </div>
                  <p className="text-gray-700"><strong>Starvation diets</strong> that destroy your metabolism</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-red-600 font-bold text-sm">✗</span>
                  </div>
                  <p className="text-gray-700"><strong>Extreme workouts</strong> that burn you out in weeks</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-red-600 font-bold text-sm">✗</span>
                  </div>
                  <p className="text-gray-700"><strong>"One-size-fits-all"</strong> programs that ignore your body type</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-red-600 font-bold text-sm">✗</span>
                  </div>
                  <p className="text-gray-700"><strong>Complicated meal plans</strong> that are impossible to follow</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-green-700">Our Revolutionary Approach:</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <p className="text-gray-700"><strong>Metabolic Reset Protocol</strong> that turns your body into a fat-burning machine</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <p className="text-gray-700"><strong>15-minute workouts</strong> that fit into any schedule</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <p className="text-gray-700"><strong>Personalized meal plans</strong> based on your food preferences</p>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <p className="text-gray-700"><strong>Simple 3-step system</strong> anyone can follow</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/products" className="bg-red-600 text-white px-10 py-4 rounded-lg font-bold text-xl hover:bg-red-700 transition-all transform hover:scale-105 shadow-lg inline-flex items-center">
              <Zap className="w-6 h-6 mr-2" />
              YES! I Want This System Now
            </Link>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section id="testimonials" className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-heading text-4xl font-bold text-gray-900 mb-6">
              <span className="text-red-600">WARNING:</span> These Results Are NOT Typical...
              <span className="block text-2xl text-gray-700 mt-2">They're BETTER Than Average!</span>
            </h2>
            <p className="text-xl text-gray-700 max-w-4xl mx-auto">
              See what happens when you finally use a system that actually works with your body instead of against it
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border-2 border-green-200 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-4 py-1 rounded-full text-sm font-bold">
                VERIFIED RESULT
              </div>
              <div className="flex items-center mb-4 mt-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-800 mb-6 font-medium text-lg">
                "I lost 32 pounds in just 30 days! I couldn't believe it when I stepped on the scale.
                This is the ONLY thing that ever worked for me."
              </p>
              <div className="flex items-center">
                <div className="w-16 h-16 bg-green-200 rounded-full flex items-center justify-center mr-4">
                  <span className="font-bold text-green-800 text-lg">SM</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 text-lg">Sarah M.</div>
                  <div className="text-green-700 font-semibold">Lost 32 lbs in 30 days</div>
                  <div className="text-sm text-gray-600">Age 42, Mother of 3</div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-blue-200 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-bold">
                VERIFIED RESULT
              </div>
              <div className="flex items-center mb-4 mt-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-800 mb-6 font-medium text-lg">
                "Down 47 pounds and my doctor says my blood pressure is perfect now.
                My wife says I look 10 years younger!"
              </p>
              <div className="flex items-center">
                <div className="w-16 h-16 bg-blue-200 rounded-full flex items-center justify-center mr-4">
                  <span className="font-bold text-blue-800 text-lg">MJ</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 text-lg">Mike J.</div>
                  <div className="text-blue-700 font-semibold">Lost 47 lbs in 8 weeks</div>
                  <div className="text-sm text-gray-600">Age 55, Executive</div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 border-2 border-purple-200 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-bold">
                VERIFIED RESULT
              </div>
              <div className="flex items-center mb-4 mt-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-800 mb-6 font-medium text-lg">
                "I've tried everything for 20 years. This is the first time I lost weight
                AND kept it off. It's been 6 months and I'm still at my goal weight!"
              </p>
              <div className="flex items-center">
                <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mr-4">
                  <span className="font-bold text-purple-800 text-lg">LK</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 text-lg">Lisa K.</div>
                  <div className="text-purple-700 font-semibold">Lost 28 lbs, kept it off 6 months</div>
                  <div className="text-sm text-gray-600">Age 38, Teacher</div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="bg-yellow-100 border-2 border-yellow-400 rounded-lg p-6 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                🚨 ATTENTION: Results Like These Are Only Possible With Our Complete System
              </h3>
              <p className="text-gray-700 font-medium">
                Don't waste another day with methods that don't work. Get the EXACT system these people used.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-16 px-4 bg-gradient-to-br from-red-600 to-red-700 text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-heading text-4xl font-bold mb-4">
              🔥 Get The Complete SlimSolutions System
            </h2>
            <p className="text-xl text-red-100 max-w-3xl mx-auto">
              Everything you need to lose 10-30 lbs in the next 30 days - guaranteed or your money back!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <h3 className="text-3xl font-bold">What You Get Today:</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-lg">The Metabolic Reset Protocol</p>
                    <p className="text-red-100">Turn your body into a 24/7 fat-burning machine (Value: $197)</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-lg">Personalized Meal Plans</p>
                    <p className="text-red-100">Custom plans based on your food preferences (Value: $147)</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-lg">15-Minute Fat-Burning Workouts</p>
                    <p className="text-red-100">No gym required, works for any fitness level (Value: $97)</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-lg">24/7 Support Community</p>
                    <p className="text-red-100">Private Facebook group with 50,000+ members (Value: $97)</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-6 h-6 text-green-300 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-lg">Progress Tracking App</p>
                    <p className="text-red-100">Track your results and stay motivated (Value: $47)</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white text-gray-900 rounded-2xl p-8 shadow-2xl relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-6 py-2 rounded-full font-bold">
                LIMITED TIME OFFER
              </div>

              <div className="text-center mt-4">
                <div className="text-gray-500 line-through text-xl mb-2">Regular Price: $585</div>
                <div className="text-5xl font-bold text-red-600 mb-2">$97</div>
                <div className="text-green-600 font-bold text-lg mb-6">Save $488 Today!</div>

                <Link href="/products" className="block w-full bg-green-600 text-white px-8 py-4 rounded-lg font-bold text-xl hover:bg-green-700 transition-all transform hover:scale-105 shadow-lg mb-4">
                  🎁 GET INSTANT ACCESS NOW
                </Link>

                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center justify-center">
                    <Shield className="w-4 h-4 mr-2" />
                    60-Day Money-Back Guarantee
                  </div>
                  <div className="flex items-center justify-center">
                    <Clock className="w-4 h-4 mr-2" />
                    Instant Digital Access
                  </div>
                  <div className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    No Monthly Fees
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final Urgent CTA */}
      <section className="py-16 px-4 bg-black text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6 text-yellow-400">
            ⚠️ LAST CHANCE WARNING ⚠️
          </h2>
          <p className="text-2xl mb-4">
            Don't Let Another Year Go By Feeling Unhappy With Your Body
          </p>
          <p className="text-xl text-gray-300 mb-8">
            This special 83% discount expires at midnight tonight. After that, the price goes back to $585.
            Don't miss your chance to get the same system that helped 50,247 people lose weight.
          </p>

          <div className="bg-red-600 text-white p-6 rounded-lg mb-8 max-w-2xl mx-auto">
            <p className="text-lg font-bold mb-2">🔥 ONLY 47 SPOTS LEFT AT THIS PRICE 🔥</p>
            <p className="text-yellow-300">Timer: 23:47:32 remaining</p>
          </div>

          <Link href="/products" className="bg-yellow-400 text-black px-12 py-6 rounded-lg font-bold text-2xl hover:bg-yellow-300 transition-all transform hover:scale-105 shadow-lg inline-flex items-center">
            <Zap className="w-8 h-8 mr-3" />
            SECURE MY TRANSFORMATION NOW
          </Link>

          <p className="text-gray-400 text-sm mt-6">
            60-Day Money-Back Guarantee • Instant Access • One-Time Payment
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="font-heading text-2xl font-bold mb-4">SlimSolutions</h3>
              <p className="text-gray-400 mb-6">
                Transforming lives through proven weight loss solutions since 2020.
                Join the 50,000+ success stories.
              </p>
            </div>

            <div>
              <h4 className="font-heading text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link href="/#testimonials" className="text-gray-400 hover:text-white transition-colors">Success Stories</Link></li>
                <li><Link href="/products" className="text-gray-400 hover:text-white transition-colors">Products</Link></li>
                <li><Link href="/#features" className="text-gray-400 hover:text-white transition-colors">How It Works</Link></li>
                <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-heading text-lg font-semibold mb-4">Legal</h4>
              <ul className="space-y-2">
                <li><Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-gray-400 hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/disclaimer" className="text-gray-400 hover:text-white transition-colors">Disclaimer</Link></li>
                <li><Link href="/refund" className="text-gray-400 hover:text-white transition-colors">Refund Policy</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2024 SlimSolutions. All rights reserved. |
              <span className="text-xs"> Results may vary. Individual results are not guaranteed. Consult your doctor before starting any weight loss program.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

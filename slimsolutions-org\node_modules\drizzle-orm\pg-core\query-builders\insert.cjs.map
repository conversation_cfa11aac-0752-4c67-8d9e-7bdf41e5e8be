{"version": 3, "sources": ["../../../src/pg-core/query-builders/insert.ts"], "sourcesContent": ["import type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { IndexColumn } from '~/pg-core/indexes.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable, TableConfig } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { InferInsertModel } from '~/table.ts';\nimport { Columns, getTableName, Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { haveSameKeys, mapUpdateSet, type NeonAuthToken, orderSelectedFields } from '~/utils.ts';\nimport type { AnyPgColumn, PgColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport { QueryBuilder } from './query-builder.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { PgUpdateSetSource } from './update.ts';\n\nexport interface PgInsertConfig<TTable extends PgTable = PgTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[] | PgInsertSelectQueryBuilder<TTable> | SQL;\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturningFields?: SelectedFieldsFlat;\n\treturning?: SelectedFieldsOrdered;\n\tselect?: boolean;\n\toverridingSystemValue_?: boolean;\n}\n\nexport type PgInsertValue<TTable extends PgTable<TableConfig>, OverrideT extends boolean = false> =\n\t& {\n\t\t[Key in keyof InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>]:\n\t\t\t| InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>[Key]\n\t\t\t| SQL\n\t\t\t| Placeholder;\n\t}\n\t& {};\n\nexport type PgInsertSelectQueryBuilder<TTable extends PgTable> = TypedQueryBuilder<\n\t{ [K in keyof TTable['$inferInsert']]: AnyPgColumn | SQL | SQL.Aliased | TTable['$inferInsert'][K] }\n>;\n\nexport class PgInsertBuilder<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tOverrideT extends boolean = false,\n> {\n\tstatic readonly [entityKind]: string = 'PgInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t\tprivate overridingSystemValue_?: boolean,\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverridingSystemValue(): Omit<PgInsertBuilder<TTable, TQueryResult, true>, 'overridingSystemValue'> {\n\t\tthis.overridingSystemValue_ = true;\n\t\treturn this as any;\n\t}\n\n\tvalues(value: PgInsertValue<TTable, OverrideT>): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable, OverrideT>[]): PgInsertBase<TTable, TQueryResult>;\n\tvalues(\n\t\tvalues: PgInsertValue<TTable, OverrideT> | PgInsertValue<TTable, OverrideT>[],\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new PgInsertBase(\n\t\t\tthis.table,\n\t\t\tmappedValues,\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t\tfalse,\n\t\t\tthis.overridingSystemValue_,\n\t\t).setToken(this.authToken) as any;\n\t}\n\n\tselect(selectQuery: (qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: (qb: QueryBuilder) => SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(\n\t\tselectQuery:\n\t\t\t| SQL\n\t\t\t| PgInsertSelectQueryBuilder<TTable>\n\t\t\t| ((qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable> | SQL),\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tconst select = typeof selectQuery === 'function' ? selectQuery(new QueryBuilder()) : selectQuery;\n\n\t\tif (\n\t\t\t!is(select, SQL)\n\t\t\t&& !haveSameKeys(this.table[Columns], select._.selectedFields)\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t'Insert select error: selected fields are not the same or are in a different order compared to the table definition',\n\t\t\t);\n\t\t}\n\n\t\treturn new PgInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n\t}\n}\n\nexport type PgInsertWithout<T extends AnyPgInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tPgInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['selectedFields'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type PgInsertReturning<\n\tT extends AnyPgInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tTSelectedFields,\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type PgInsertReturningAll<T extends AnyPgInsert, TDynamic extends boolean> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['_']['columns'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface PgInsertOnConflictDoUpdateConfig<T extends AnyPgInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: PgUpdateSetSource<T['_']['table']>;\n}\n\nexport type PgInsertPrepare<T extends AnyPgInsert> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgInsertDynamic<T extends AnyPgInsert> = PgInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgInsert = PgInsertBase<any, any, any, any, any, any>;\n\nexport type PgInsert<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = ColumnsSelection | undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgInsertBase<TTable, TQueryResult, TSelectedFields, TReturning, true, never>;\n\nexport interface PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tTypedQueryBuilder<\n\t\tTSelectedFields,\n\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t>,\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tTypedQueryBuilder<\n\t\t\tTSelectedFields,\n\t\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t\t>,\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgInsert';\n\n\tprivate config: PgInsertConfig<TTable>;\n\tprotected cacheConfig?: WithCacheConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: PgInsertConfig['values'],\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t\tselect?: boolean,\n\t\toverridingSystemValue_?: boolean,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values: values as any, withList, select, overridingSystemValue_ };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): PgInsertWithout<PgInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgInsertWithout<PgInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgInsertWithout<AnyPgInsert, TDynamic, 'returning'> {\n\t\tthis.config.returningFields = fields;\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(\n\t\tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict = sql`do nothing`;\n\t\t} else {\n\t\t\tlet targetColumn = '';\n\t\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(\n\t\tconfig: PgInsertOnConflictDoUpdateConfig<this>,\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tlet targetColumn = '';\n\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\t\tthis.config.onConflict = sql`(${\n\t\t\tsql.raw(targetColumn)\n\t\t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, undefined, {\n\t\t\t\ttype: 'insert',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t}, this.cacheConfig);\n\t\t});\n\t}\n\n\tprepare(name: string): PgInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): this['_']['selectedFields'] {\n\t\treturn (\n\t\t\tthis.config.returningFields\n\t\t\t\t? new Proxy(\n\t\t\t\t\tthis.config.returningFields,\n\t\t\t\t\tnew SelectionProxyHandler({\n\t\t\t\t\t\talias: getTableName(this.config.table),\n\t\t\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\t\t\tsqlBehavior: 'error',\n\t\t\t\t\t}),\n\t\t\t\t)\n\t\t\t\t: undefined\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA+B;AAa/B,2BAA6B;AAE7B,6BAAsC;AAEtC,iBAAgC;AAGhC,mBAA6C;AAC7C,qBAAuB;AACvB,mBAAoF;AAEpF,IAAAA,gBAAiC;AACjC,2BAA6B;AA4BtB,MAAM,gBAIX;AAAA,EAGD,YACS,OACA,SACA,SACA,UACA,wBACP;AALO;AACA;AACA;AACA;AACA;AAAA,EACN;AAAA,EARH,QAAiB,wBAAU,IAAY;AAAA,EAU/B;AAAA;AAAA,EAER,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,wBAAoG;AACnG,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACR;AAAA,EAIA,OACC,QACqC;AACrC,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,mBAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,QAAI,kBAAG,UAAU,cAAG,IAAI,WAAW,IAAI,iBAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAED,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN,EAAE,SAAS,KAAK,SAAS;AAAA,EAC1B;AAAA,EAMA,OACC,aAIqC;AACrC,UAAM,SAAS,OAAO,gBAAgB,aAAa,YAAY,IAAI,kCAAa,CAAC,IAAI;AAErF,QACC,KAAC,kBAAG,QAAQ,cAAG,KACZ,KAAC,2BAAa,KAAK,MAAM,oBAAO,GAAG,OAAO,EAAE,cAAc,GAC5D;AACD,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,WAAO,IAAI,aAAa,KAAK,OAAO,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI;AAAA,EAC5F;AACD;AAkGO,MAAM,qBASH,kCAQV;AAAA,EAMC,YACC,OACA,QACQ,SACA,SACR,UACA,QACA,wBACC;AACD,UAAM;AANE;AACA;AAMR,SAAK,SAAS,EAAE,OAAO,QAAuB,UAAU,QAAQ,uBAAuB;AAAA,EACxF;AAAA,EAhBA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACE;AAAA,EAuCV,UACC,SAA6B,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO,GACb;AACtD,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,gBAAY,kCAA8B,MAAM;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,oBACC,SAAgE,CAAC,GACe;AAChF,QAAI,OAAO,WAAW,QAAW;AAChC,WAAK,OAAO,aAAa;AAAA,IAC1B,OAAO;AACN,UAAI,eAAe;AACnB,qBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAE7E,YAAM,WAAW,OAAO,QAAQ,wBAAa,OAAO,KAAK,KAAK;AAC9D,WAAK,OAAO,aAAa,kBAAO,eAAI,IAAI,YAAY,CAAC,IAAI,QAAQ;AAAA,IAClE;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,mBACC,QACgF;AAChF,QAAI,OAAO,UAAU,OAAO,eAAe,OAAO,WAAW;AAC5D,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AACA,UAAM,WAAW,OAAO,QAAQ,wBAAa,OAAO,KAAK,KAAK;AAC9D,UAAM,iBAAiB,OAAO,cAAc,wBAAa,OAAO,WAAW,KAAK;AAChF,UAAM,cAAc,OAAO,WAAW,wBAAa,OAAO,QAAQ,KAAK;AACvE,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,WAAO,2BAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,QAAI,eAAe;AACnB,mBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAC7E,SAAK,OAAO,aAAa,kBACxB,eAAI,IAAI,YAAY,CACrB,IAAI,cAAc,kBAAkB,MAAM,GAAG,QAAQ,GAAG,WAAW;AACnE,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAsC;AAC9C,WAAO,sBAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,MAAM,QAAW;AAAA,QACvF,MAAM;AAAA,QACN,YAAQ,gCAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C,GAAG,KAAK,WAAW;AAAA,IACpB,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEQ;AAAA;AAAA,EAER,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;AAAA,IACjE,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,oBAAiD;AAChD,WACC,KAAK,OAAO,kBACT,IAAI;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,IAAI,6CAAsB;AAAA,QACzB,WAAO,2BAAa,KAAK,OAAO,KAAK;AAAA,QACrC,oBAAoB;AAAA,QACpB,aAAa;AAAA,MACd,CAAC;AAAA,IACF,IACE;AAAA,EAEL;AAAA,EAEA,WAAkC;AACjC,WAAO;AAAA,EACR;AACD;", "names": ["import_utils"]}
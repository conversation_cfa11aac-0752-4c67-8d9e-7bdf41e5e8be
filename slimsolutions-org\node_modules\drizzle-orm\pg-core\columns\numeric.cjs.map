{"version": 3, "sources": ["../../../src/pg-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgNumericBuilderInitial<TName extends string> = PgNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgNumeric'>> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tprecision: number | undefined;\n\t\tscale: number | undefined;\n\t}\n> {\n\tstatic override readonly [entityKind]: string = 'PgNumericBuilder';\n\n\tconstructor(name: T['name'], precision?: number, scale?: number) {\n\t\tsuper(name, 'string', 'PgNumeric');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumeric<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgNumeric<T extends ColumnBaseConfig<'string', 'PgNumeric'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgNumeric';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\toverride mapFromDriverValue(value: unknown): string {\n\t\tif (typeof value === 'string') return value;\n\n\t\treturn String(value);\n\t}\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport type PgNumericNumberBuilderInitial<TName extends string> = PgNumericNumberBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgNumericNumber';\n\tdata: number;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgNumericNumberBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgNumericNumber'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{\n\t\t\tprecision: number | undefined;\n\t\t\tscale: number | undefined;\n\t\t}\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgNumericNumberBuilder';\n\n\tconstructor(name: T['name'], precision?: number, scale?: number) {\n\t\tsuper(name, 'number', 'PgNumericNumber');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumericNumber<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumericNumber<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgNumericNumber<T extends ColumnBaseConfig<'number', 'PgNumericNumber'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgNumericNumber';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericNumberBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\toverride mapFromDriverValue(value: unknown): number {\n\t\tif (typeof value === 'number') return value;\n\n\t\treturn Number(value);\n\t}\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport type PgNumericBigIntBuilderInitial<TName extends string> = PgNumericBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'PgNumericBigInt';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgNumericBigIntBuilder<T extends ColumnBuilderBaseConfig<'bigint', 'PgNumericBigInt'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{\n\t\t\tprecision: number | undefined;\n\t\t\tscale: number | undefined;\n\t\t}\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgNumericBigIntBuilder';\n\n\tconstructor(name: T['name'], precision?: number, scale?: number) {\n\t\tsuper(name, 'bigint', 'PgNumericBigInt');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumericBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumericBigInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgNumericBigInt<T extends ColumnBaseConfig<'bigint', 'PgNumericBigInt'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgNumericBigInt';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericBigIntBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\toverride mapFromDriverValue = BigInt;\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport type PgNumericConfig<T extends 'string' | 'number' | 'bigint' = 'string' | 'number' | 'bigint'> =\n\t| { precision: number; scale?: number; mode?: T }\n\t| { precision?: number; scale: number; mode?: T }\n\t| { precision?: number; scale?: number; mode: T };\n\nexport function numeric<TMode extends 'string' | 'number' | 'bigint'>(\n\tconfig?: PgNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? PgNumericNumberBuilderInitial<''>\n\t: Equal<TMode, 'bigint'> extends true ? PgNumericBigIntBuilderInitial<''>\n\t: PgNumericBuilderInitial<''>;\nexport function numeric<TName extends string, TMode extends 'string' | 'number' | 'bigint'>(\n\tname: TName,\n\tconfig?: PgNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? PgNumericNumberBuilderInitial<TName>\n\t: Equal<TMode, 'bigint'> extends true ? PgNumericBigIntBuilderInitial<TName>\n\t: PgNumericBuilderInitial<TName>;\nexport function numeric(a?: string | PgNumericConfig, b?: PgNumericConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgNumericConfig>(a, b);\n\tconst mode = config?.mode;\n\treturn mode === 'number'\n\t\t? new PgNumericNumberBuilder(name, config?.precision, config?.scale)\n\t\t: mode === 'bigint'\n\t\t? new PgNumericBigIntBuilder(name, config?.precision, config?.scale)\n\t\t: new PgNumericBuilder(name, config?.precision, config?.scale);\n}\n\nexport const decimal = numeric;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAmD;AACnD,oBAA0C;AAWnC,MAAM,yBAAmF,8BAM9F;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAgB;AAChE,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAqE,uBAAY;AAAA,EAC7F,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAAuC;AAC/F,UAAM,OAAO,MAAM;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,QAAQ,OAAO;AAAA,EACrB;AAAA,EAES,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;AAAA,IACjC;AAAA,EACD;AACD;AAWO,MAAM,+BACJ,8BAOT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAgB;AAChE,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAAiF,uBAAY;AAAA,EACzG,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAA6C;AACrG,UAAM,OAAO,MAAM;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,QAAQ,OAAO;AAAA,EACrB;AAAA,EAES,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAES,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;AAAA,IACjC;AAAA,EACD;AACD;AAWO,MAAM,+BACJ,8BAOT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAgB;AAChE,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAAiF,uBAAY;AAAA,EACzG,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAA6C;AACrG,UAAM,OAAO,MAAM;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,QAAQ,OAAO;AAAA,EACrB;AAAA,EAES,qBAAqB;AAAA,EAErB,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;AAAA,IACjC;AAAA,EACD;AACD;AAkBO,SAAS,QAAQ,GAA8B,GAAqB;AAC1E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,QAAM,OAAO,QAAQ;AACrB,SAAO,SAAS,WACb,IAAI,uBAAuB,MAAM,QAAQ,WAAW,QAAQ,KAAK,IACjE,SAAS,WACT,IAAI,uBAAuB,MAAM,QAAQ,WAAW,QAAQ,KAAK,IACjE,IAAI,iBAAiB,MAAM,QAAQ,WAAW,QAAQ,KAAK;AAC/D;AAEO,MAAM,UAAU;", "names": []}
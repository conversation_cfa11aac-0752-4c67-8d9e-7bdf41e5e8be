{"version": 3, "sources": ["../../src/planetscale-serverless/session.ts"], "sourcesContent": ["import type { Client, Connection, ExecutedQuery, Transaction } from '@planetscale/database';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type { SelectedFieldsOrdered } from '~/mysql-core/query-builders/select.types.ts';\nimport {\n\tMySqlPreparedQuery,\n\ttype MySqlPreparedQueryConfig,\n\ttype MySqlPreparedQueryHKT,\n\ttype MySqlQueryResultHKT,\n\tMySqlSession,\n\tMySqlTransaction,\n} from '~/mysql-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport class PlanetScalePreparedQuery<T extends MySqlPreparedQueryConfig> extends MySqlPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'PlanetScalePreparedQuery';\n\n\tprivate rawQuery = { as: 'object' } as const;\n\tprivate query = { as: 'array' } as const;\n\n\tconstructor(\n\t\tprivate client: Client | Transaction | Connection,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\t// Keys that were used in $default and the value that was generated for them\n\t\tprivate generatedIds?: Record<string, unknown>[],\n\t\t// Keys that should be returned, it has the column with all properries + key from object\n\t\tprivate returningIds?: SelectedFieldsOrdered,\n\t) {\n\t\tsuper(cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\tconst {\n\t\t\tfields,\n\t\t\tclient,\n\t\t\tqueryString,\n\t\t\trawQuery,\n\t\t\tquery,\n\t\t\tjoinsNotNullableMap,\n\t\t\tcustomResultMapper,\n\t\t\treturningIds,\n\t\t\tgeneratedIds,\n\t\t} = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst res = await this.queryWithCache(queryString, params, async () => {\n\t\t\t\treturn await client.execute(queryString, params, rawQuery);\n\t\t\t});\n\n\t\t\tconst insertId = Number.parseFloat(res.insertId);\n\t\t\tconst affectedRows = res.rowsAffected;\n\n\t\t\t// for each row, I need to check keys from\n\t\t\tif (returningIds) {\n\t\t\t\tconst returningResponse = [];\n\t\t\t\tlet j = 0;\n\t\t\t\tfor (let i = insertId; i < insertId + affectedRows; i++) {\n\t\t\t\t\tfor (const column of returningIds) {\n\t\t\t\t\t\tconst key = returningIds[0]!.path[0]!;\n\t\t\t\t\t\tif (is(column.field, Column)) {\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\tif (column.field.primary && column.field.autoIncrement) {\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: i });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (column.field.defaultFn && generatedIds) {\n\t\t\t\t\t\t\t\t// generatedIds[rowIdx][key]\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: generatedIds[j]![key] });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tj++;\n\t\t\t\t}\n\t\t\t\treturn returningResponse;\n\t\t\t}\n\t\t\treturn res;\n\t\t}\n\t\tconst { rows } = await this.queryWithCache(queryString, params, async () => {\n\t\t\treturn await client.execute(queryString, params, query);\n\t\t});\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows as unknown[][]);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row as unknown[], joinsNotNullableMap));\n\t}\n\n\toverride iterator(_placeholderValues?: Record<string, unknown>): AsyncGenerator<T['iterator']> {\n\t\tthrow new Error('Streaming is not supported by the PlanetScale Serverless driver');\n\t}\n}\n\nexport interface PlanetscaleSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class PlanetscaleSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlSession<MySqlQueryResultHKT, PlanetScalePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PlanetscaleSession';\n\n\tprivate logger: Logger;\n\tprivate client: Client | Transaction | Connection;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate baseClient: Client | Connection,\n\t\tdialect: MySqlDialect,\n\t\ttx: Transaction | undefined,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: PlanetscaleSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.client = tx ?? baseClient;\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends MySqlPreparedQueryConfig = MySqlPreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): MySqlPreparedQuery<T> {\n\t\treturn new PlanetScalePreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t\tgeneratedIds,\n\t\t\treturningIds,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<ExecutedQuery> {\n\t\tthis.logger.logQuery(query, params);\n\n\t\treturn await this.client.execute(query, params, { as: 'array' });\n\t}\n\n\tasync queryObjects(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<ExecutedQuery> {\n\t\treturn this.client.execute(query, params, { as: 'object' });\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\n\t\treturn this.client.execute<T>(querySql.sql, querySql.params, { as: 'object' }).then((\n\t\t\teQuery,\n\t\t) => eQuery.rows);\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<{ rows: [{ count: string }] }>(sql);\n\n\t\treturn Number(\n\t\t\tres['rows'][0]['count'],\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: PlanetScaleTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn this.baseClient.transaction((pstx) => {\n\t\t\tconst session = new PlanetscaleSession(this.baseClient, this.dialect, pstx, this.schema, this.options);\n\t\t\tconst tx = new PlanetScaleTransaction<TFullSchema, TSchema>(\n\t\t\t\tthis.dialect,\n\t\t\t\tsession as MySqlSession<any, any, any, any>,\n\t\t\t\tthis.schema,\n\t\t\t);\n\t\t\treturn transaction(tx);\n\t\t});\n\t}\n}\n\nexport class PlanetScaleTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlTransaction<PlanetscaleQueryResultHKT, PlanetScalePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PlanetScaleTransaction';\n\n\tconstructor(\n\t\tdialect: MySqlDialect,\n\t\tsession: MySqlSession,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tnestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema, nestedIndex, 'planetscale');\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: PlanetScaleTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new PlanetScaleTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface PlanetscaleQueryResultHKT extends MySqlQueryResultHKT {\n\ttype: ExecutedQuery;\n}\n\nexport interface PlanetScalePreparedQueryHKT extends MySqlPreparedQueryHKT {\n\ttype: PlanetScalePreparedQuery<Assume<this['config'], MySqlPreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,kBAAsC;AAEtC,oBAAuB;AACvB,oBAA+B;AAE/B,oBAA2B;AAG3B,qBAOO;AAEP,iBAA4D;AAC5D,mBAA0C;AAEnC,MAAM,iCAAqE,kCAAsB;AAAA,EAMvG,YACS,QACA,aACA,QACA,QACR,OACA,eAIA,aACQ,QACA,oBAEA,cAEA,cACP;AACD,UAAM,OAAO,eAAe,WAAW;AAjB/B;AACA;AACA;AACA;AAOA;AACA;AAEA;AAEA;AAAA,EAGT;AAAA,EAxBA,QAA0B,wBAAU,IAAY;AAAA,EAExC,WAAW,EAAE,IAAI,SAAS;AAAA,EAC1B,QAAQ,EAAE,IAAI,QAAQ;AAAA,EAuB9B,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAI;AACJ,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,MAAM,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AACtE,eAAO,MAAM,OAAO,QAAQ,aAAa,QAAQ,QAAQ;AAAA,MAC1D,CAAC;AAED,YAAM,WAAW,OAAO,WAAW,IAAI,QAAQ;AAC/C,YAAM,eAAe,IAAI;AAGzB,UAAI,cAAc;AACjB,cAAM,oBAAoB,CAAC;AAC3B,YAAI,IAAI;AACR,iBAAS,IAAI,UAAU,IAAI,WAAW,cAAc,KAAK;AACxD,qBAAW,UAAU,cAAc;AAClC,kBAAM,MAAM,aAAa,CAAC,EAAG,KAAK,CAAC;AACnC,oBAAI,kBAAG,OAAO,OAAO,oBAAM,GAAG;AAE7B,kBAAI,OAAO,MAAM,WAAW,OAAO,MAAM,eAAe;AACvD,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAAA,cACpC;AACA,kBAAI,OAAO,MAAM,aAAa,cAAc;AAE3C,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAG,GAAG,EAAE,CAAC;AAAA,cACxD;AAAA,YACD;AAAA,UACD;AACA;AAAA,QACD;AACA,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR;AACA,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AAC3E,aAAO,MAAM,OAAO,QAAQ,aAAa,QAAQ,KAAK;AAAA,IACvD,CAAC;AAED,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAmB;AAAA,IAC9C;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAkB,mBAAmB,CAAC;AAAA,EACpG;AAAA,EAES,SAAS,oBAA6E;AAC9F,UAAM,IAAI,MAAM,iEAAiE;AAAA,EAClF;AACD;AAOO,MAAM,2BAGH,4BAAqF;AAAA,EAO9F,YACS,YACR,SACA,IACQ,QACA,UAAqC,CAAC,GAC7C;AACD,UAAM,OAAO;AANL;AAGA;AACA;AAGR,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,sBAAU;AAAA,EAC7C;AAAA,EAjBA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EACA;AAAA,EAeR,aACC,OACA,QACA,oBACA,cACA,cACA,eAIA,aACwB;AACxB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAA2C;AACrE,SAAK,OAAO,SAAS,OAAO,MAAM;AAElC,WAAO,MAAM,KAAK,OAAO,QAAQ,OAAO,QAAQ,EAAE,IAAI,QAAQ,CAAC;AAAA,EAChE;AAAA,EAEA,MAAM,aACL,OACA,QACyB;AACzB,WAAO,KAAK,OAAO,QAAQ,OAAO,QAAQ,EAAE,IAAI,SAAS,CAAC;AAAA,EAC3D;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAElD,WAAO,KAAK,OAAO,QAAW,SAAS,KAAK,SAAS,QAAQ,EAAE,IAAI,SAAS,CAAC,EAAE,KAAK,CACnF,WACI,OAAO,IAAI;AAAA,EACjB;AAAA,EAEA,MAAe,MAAMA,MAA2B;AAC/C,UAAM,MAAM,MAAM,KAAK,QAAuCA,IAAG;AAEjE,WAAO;AAAA,MACN,IAAI,MAAM,EAAE,CAAC,EAAE,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EAES,YACR,aACa;AACb,WAAO,KAAK,WAAW,YAAY,CAAC,SAAS;AAC5C,YAAM,UAAU,IAAI,mBAAmB,KAAK,YAAY,KAAK,SAAS,MAAM,KAAK,QAAQ,KAAK,OAAO;AACrG,YAAM,KAAK,IAAI;AAAA,QACd,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACN;AACA,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,+BAGH,gCAA+F;AAAA,EACxG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YACC,SACA,SACA,QACA,cAAc,GACb;AACD,UAAM,SAAS,SAAS,QAAQ,aAAa,aAAa;AAAA,EAC3D;AAAA,EAEA,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["sql"]}
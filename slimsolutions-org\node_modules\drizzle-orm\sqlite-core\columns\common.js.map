{"version": 3, "sources": ["../../../src/sqlite-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport { Column } from '~/column.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { ForeignKey, UpdateDeleteAction } from '~/sqlite-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/sqlite-core/foreign-keys.ts';\nimport type { AnySQLiteTable, SQLiteTable } from '~/sqlite-core/table.ts';\nimport type { Update } from '~/utils.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\n\nexport interface ReferenceConfig {\n\tref: () => SQLiteColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface SQLiteColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'sqlite' }> {}\n\nexport interface SQLiteGeneratedColumnConfig {\n\tmode?: 'virtual' | 'stored';\n}\n\nexport abstract class SQLiteColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = object,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'sqlite' }, TExtraConfig>\n\timplements SQLiteColumnBuilderBase<T, TTypeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteColumnBuilder';\n\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL), config?: SQLiteGeneratedColumnConfig): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: config?.mode ?? 'virtual',\n\t\t};\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: SQLiteColumn, table: SQLiteTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn ((ref, actions) => {\n\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t});\n\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t}\n\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t}\n\t\t\t\treturn builder.build(table);\n\t\t\t})(ref, actions);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteColumn<MakeColumnConfig<T, TTableName>>;\n}\n\n// To understand how to use `SQLiteColumn` and `AnySQLiteColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class SQLiteColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'sqlite' }> {\n\tstatic override readonly [entityKind]: string = 'SQLiteColumn';\n\n\tconstructor(\n\t\toverride readonly table: SQLiteTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type AnySQLiteColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = SQLiteColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n"], "mappings": "AASA,SAAS,qBAAqB;AAC9B,SAAS,cAAc;AAEvB,SAAS,kBAAkB;AAG3B,SAAS,yBAAyB;AAGlC,SAAS,qBAAqB;AAmBvB,MAAe,4BAKZ,cAEV;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAExC,oBAAuC,CAAC;AAAA,EAEhD,WACC,KACA,UAAsC,CAAC,GAChC;AACP,SAAK,kBAAkB,KAAK,EAAE,KAAK,QAAQ,CAAC;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,OACC,MACO;AACP,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,kBAAkB,IAAmC,QAElD;AACF,SAAK,OAAO,YAAY;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,MACN,MAAM,QAAQ,QAAQ;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,iBAAiB,QAAsB,OAAkC;AACxE,WAAO,KAAK,kBAAkB,IAAI,CAAC,EAAE,KAAK,QAAQ,MAAM;AACvD,cAAQ,CAACA,MAAKC,aAAY;AACzB,cAAM,UAAU,IAAI,kBAAkB,MAAM;AAC3C,gBAAM,gBAAgBD,KAAI;AAC1B,iBAAO,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,aAAa,EAAE;AAAA,QAC7D,CAAC;AACD,YAAIC,SAAQ,UAAU;AACrB,kBAAQ,SAASA,SAAQ,QAAQ;AAAA,QAClC;AACA,YAAIA,SAAQ,UAAU;AACrB,kBAAQ,SAASA,SAAQ,QAAQ;AAAA,QAClC;AACA,eAAO,QAAQ,MAAM,KAAK;AAAA,MAC3B,GAAG,KAAK,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAMD;AAGO,MAAe,qBAIZ,OAA+D;AAAA,EAGxE,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,aAAa,cAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,IACvD;AACA,UAAM,OAAO,MAAM;AAND;AAAA,EAOnB;AAAA,EAVA,QAA0B,UAAU,IAAY;AAWjD;", "names": ["ref", "actions"]}
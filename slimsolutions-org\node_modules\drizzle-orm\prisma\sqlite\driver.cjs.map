{"version": 3, "sources": ["../../../src/prisma/sqlite/driver.ts"], "sourcesContent": ["import { Prisma } from '@prisma/client';\n\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { BaseSQLiteDatabase, SQLiteAsyncDialect } from '~/sqlite-core/index.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { PrismaSQLiteSession } from './session.ts';\n\nexport type PrismaSQLiteDatabase = BaseSQLiteDatabase<'async', []>;\n\nexport type PrismaSQLiteConfig = Omit<DrizzleConfig, 'schema'>;\n\nexport function drizzle(config: PrismaSQLiteConfig = {}) {\n\tconst dialect = new SQLiteAsyncDialect();\n\tlet logger: Logger | undefined;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\treturn Prisma.defineExtension((client) => {\n\t\tconst session = new PrismaSQLiteSession(client, dialect, { logger });\n\n\t\treturn client.$extends({\n\t\t\tname: 'drizzle',\n\t\t\tclient: {\n\t\t\t\t$drizzle: new BaseSQLiteDatabase('async', dialect, session, undefined) as PrismaSQLiteDatabase,\n\t\t\t},\n\t\t});\n\t});\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAuB;AAGvB,oBAA8B;AAC9B,yBAAuD;AAEvD,qBAAoC;AAM7B,SAAS,QAAQ,SAA6B,CAAC,GAAG;AACxD,QAAM,UAAU,IAAI,sCAAmB;AACvC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,SAAO,qBAAO,gBAAgB,CAAC,WAAW;AACzC,UAAM,UAAU,IAAI,mCAAoB,QAAQ,SAAS,EAAE,OAAO,CAAC;AAEnE,WAAO,OAAO,SAAS;AAAA,MACtB,MAAM;AAAA,MACN,QAAQ;AAAA,QACP,UAAU,IAAI,sCAAmB,SAAS,SAAS,SAAS,MAAS;AAAA,MACtE;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACF;", "names": []}
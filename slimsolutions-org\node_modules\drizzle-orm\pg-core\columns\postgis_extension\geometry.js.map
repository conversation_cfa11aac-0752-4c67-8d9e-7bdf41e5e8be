{"version": 3, "sources": ["../../../../src/pg-core/columns/postgis_extension/geometry.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\nimport { parseEWKB } from './utils.ts';\n\nexport type PgGeometryBuilderInitial<TName extends string> = PgGeometryBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgGeometry';\n\tdata: [number, number];\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgGeometryBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgGeometry'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometryBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'array', 'PgGeometry');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgGeometry<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgGeometry<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgGeometry<T extends ColumnBaseConfig<'array', 'PgGeometry'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometry';\n\n\tgetSQLType(): string {\n\t\treturn 'geometry(point)';\n\t}\n\n\toverride mapFromDriverValue(value: string): [number, number] {\n\t\treturn parseEWKB(value);\n\t}\n\n\toverride mapToDriverValue(value: [number, number]): string {\n\t\treturn `point(${value[0]} ${value[1]})`;\n\t}\n}\n\nexport type PgGeometryObjectBuilderInitial<TName extends string> = PgGeometryObjectBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgGeometryObject';\n\tdata: { x: number; y: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgGeometryObjectBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgGeometryObject'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgGeometryObjectBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgGeometryObject');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgGeometryObject<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgGeometryObject<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgGeometryObject<T extends ColumnBaseConfig<'json', 'PgGeometryObject'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometryObject';\n\n\tgetSQLType(): string {\n\t\treturn 'geometry(point)';\n\t}\n\n\toverride mapFromDriverValue(value: string): { x: number; y: number } {\n\t\tconst parsed = parseEWKB(value);\n\t\treturn { x: parsed[0], y: parsed[1] };\n\t}\n\n\toverride mapToDriverValue(value: { x: number; y: number }): string {\n\t\treturn `point(${value.x} ${value.y})`;\n\t}\n}\n\nexport interface PgGeometryConfig<T extends 'tuple' | 'xy' = 'tuple' | 'xy'> {\n\tmode?: T;\n\ttype?: 'point' | (string & {});\n\tsrid?: number;\n}\n\nexport function geometry(): PgGeometryBuilderInitial<''>;\nexport function geometry<TMode extends PgGeometryConfig['mode'] & {}>(\n\tconfig?: PgGeometryConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgGeometryObjectBuilderInitial<''> : PgGeometryBuilderInitial<''>;\nexport function geometry<TName extends string, TMode extends PgGeometryConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgGeometryConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgGeometryObjectBuilderInitial<TName> : PgGeometryBuilderInitial<TName>;\nexport function geometry(a?: string | PgGeometryConfig, b?: PgGeometryConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgGeometryConfig>(a, b);\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgGeometryBuilder(name);\n\t}\n\treturn new PgGeometryObjectBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAqB,8BAA8B;AACnD,SAAS,UAAU,uBAAuB;AAC1C,SAAS,iBAAiB;AAWnB,MAAM,0BAAoF,gBAAmB;AAAA,EACnH,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,SAAS,YAAY;AAAA,EAClC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,mBAAsE,SAAY;AAAA,EAC9F,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAiC;AAC5D,WAAO,UAAU,KAAK;AAAA,EACvB;AAAA,EAES,iBAAiB,OAAiC;AAC1D,WAAO,SAAS,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EACrC;AACD;AAWO,MAAM,gCACJ,gBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,kBAAkB;AAAA,EACvC;AAAA;AAAA,EAGS,MACR,OACoD;AACpD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,yBAAiF,SAAY;AAAA,EACzG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAyC;AACpE,UAAM,SAAS,UAAU,KAAK;AAC9B,WAAO,EAAE,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,EAAE;AAAA,EACrC;AAAA,EAES,iBAAiB,OAAyC;AAClE,WAAO,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EACnC;AACD;AAgBO,SAAS,SAAS,GAA+B,GAAsB;AAC7E,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAyC,GAAG,CAAC;AACtE,MAAI,CAAC,QAAQ,QAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,kBAAkB,IAAI;AAAA,EAClC;AACA,SAAO,IAAI,wBAAwB,IAAI;AACxC;", "names": []}
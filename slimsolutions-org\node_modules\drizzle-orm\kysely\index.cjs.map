{"version": 3, "sources": ["../../src/kysely/index.ts"], "sourcesContent": ["import type { ColumnType } from 'kysely';\nimport type { InferInsertModel, InferSelectModel, MapColumnName, Table } from '~/table.ts';\nimport type { Simplify } from '~/utils.ts';\n\nexport type Kyselify<T extends Table> = Simplify<\n\t{\n\t\t[Key in keyof T['_']['columns'] & string as MapColumnName<Key, T['_']['columns'][Key], true>]: ColumnType<\n\t\t\t// select\n\t\t\tInferSelectModel<T, { dbColumnNames: true }>[MapColumnName<Key, T['_']['columns'][Key], true>],\n\t\t\t// insert\n\t\t\tMapColumnName<Key, T['_']['columns'][Key], true> extends keyof InferInsertModel<\n\t\t\t\tT,\n\t\t\t\t{ dbColumnNames: true }\n\t\t\t> ? InferInsertModel<T, { dbColumnNames: true }>[MapColumnName<Key, T['_']['columns'][Key], true>]\n\t\t\t\t: never,\n\t\t\t// update\n\t\t\tMapColumnName<Key, T['_']['columns'][Key], true> extends keyof InferInsertModel<\n\t\t\t\tT,\n\t\t\t\t{ dbColumnNames: true }\n\t\t\t> ? InferInsertModel<T, { dbColumnNames: true }>[MapColumnName<Key, T['_']['columns'][Key], true>]\n\t\t\t\t: never\n\t\t>;\n\t}\n>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}